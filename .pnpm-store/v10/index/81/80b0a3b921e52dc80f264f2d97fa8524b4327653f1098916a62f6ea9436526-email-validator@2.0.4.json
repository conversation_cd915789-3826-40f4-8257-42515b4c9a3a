{"name": "email-validator", "version": "2.0.4", "requiresBuild": false, "files": {"package.json": {"checkedAt": 1752045729616, "integrity": "sha512-vYEVzzRuDrj3NFUk7pcepz2aIjaDSX+Xgx+GH/8apioE/sMGmAiD/NOQ16+pJA3gzpRNmt5oVpmRtt/8IDRgxw==", "mode": 438, "size": 1131}, ".travis.yml": {"checkedAt": 1752045729623, "integrity": "sha512-5N2pgQwGwp4BD6Cf1n4KFottGsdY4eZKE27Bw8eZ0kz8cEpIYYs9FPGPlPOEsp59E+AR5zNQ/c+zG+wT0U3O7g==", "mode": 438, "size": 208}, "CHANGELOG.md": {"checkedAt": 1752045729624, "integrity": "sha512-Ts9RZMtOrtKmEfVKF9dkapDjCr3pG23NF5QLglyoH2PrLDfgrdqBdr4JKIjK8M4FDs4WfFlU1Ypa41oZWFu/MA==", "mode": 438, "size": 715}, "index.cjs.js": {"checkedAt": 1752045729624, "integrity": "sha512-mIbv0Q7gMuImb4Mf7ydL27rxjACI56/xeaqE3bvzUWSntqOH+ZXjkLq9gWofNJZclw2FBQkr2vLB/6wpg2zUkg==", "mode": 438, "size": 993}, "index.cjs.js.map": {"checkedAt": 1752045729661, "integrity": "sha512-bY+6GsDiL1kn00uggfC73tdF1g6ZfjbstnTX3dlC8I/zZGO3biTXpsOAbVAXBAB9q+lgbFhVeXcWxlcOEuWIPA==", "mode": 438, "size": 1599}, "index.d.ts": {"checkedAt": 1752045729661, "integrity": "sha512-hH82Jsrwbc1vRV0G1CGcku2mPSFfuctR3FyU8hwVL1yr+CUpz4VSaoTKdKzRvSrZOBpmebwGkuiv6MBI+A+UrA==", "mode": 438, "size": 504}, "index.js": {"checkedAt": 1752045729662, "integrity": "sha512-gjvfvFnH7kuhdv7zLn/jAqHaYmUTKCtlaeLDt2q2C7G9SIwirk22R5Y+BXsFIQMoihXzUpKo8sKmx3tQS2z6XQ==", "mode": 438, "size": 898}, "LICENSE": {"checkedAt": 1752045729662, "integrity": "sha512-5jSBe7jiPPiI3bzYtZJoB8AWpgxL11gHg7jPrSqBW9oagOgHthJtn6z+8KWLYSRxRGZCWvdIJuIE91O+pY/pEQ==", "mode": 438, "size": 1232}, "README.md": {"checkedAt": 1752045729662, "integrity": "sha512-wSKIowepF8BuQw7QtPf2qkeiI0wv0Fm3xbnhtJa46TgL91nbhSy+T8d075SV0dASudE24s18TItPX3+TT4a/nw==", "mode": 438, "size": 1345}, "test.js": {"checkedAt": 1752045729662, "integrity": "sha512-v9ljsa1HytbXq8XMyfxTWI51M67J/+j3pV3IY/7hlUqdbD+/DvR13pZ5+HGUqHBdRjrdZkUzQCMJ7o3XzJv/Gg==", "mode": 438, "size": 3431}}}