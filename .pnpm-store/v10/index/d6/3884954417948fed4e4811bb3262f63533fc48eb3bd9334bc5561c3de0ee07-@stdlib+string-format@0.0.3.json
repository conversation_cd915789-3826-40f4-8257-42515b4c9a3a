{"name": "@stdlib/string-format", "version": "0.0.3", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1752045719127, "integrity": "sha512-J7jAJS6uUMo84Cq3xWcGZMDIJOA+s9oQifPwoA0j5kipVry59TZFxteWdKh8TMhtEIXcM1kRvgIQ1pEzaxIYVw==", "mode": 420, "size": 10174}, "NOTICE": {"checkedAt": 1752045719127, "integrity": "sha512-4cuPRRYzgfVMbZIC790aV9oIoAEVC+4xyLwcB73N6IHP34DxsSH2gT61hzrQQsrrPKyZ3lwkqLKxnRVCIg2vCw==", "mode": 420, "size": 44}, "lib/index.js": {"checkedAt": 1752045719128, "integrity": "sha512-l69Y3AezC43Jy3cWjxa1ADFZZiJkkdJYVUgzT0tR7kwLgl1C4GjcB8FYSMc0yljDgXHS0r6OHydYi+M5es+VBg==", "mode": 420, "size": 1046}, "lib/is_string.js": {"checkedAt": 1752045719129, "integrity": "sha512-VWLehPogFsAcFPAGIUC/WCAXKn54hxXo/ctpwUyj6K0vDSYnLgWjPT1JQC7cZad4o1ILUTDd5Ug236Va4XNUyg==", "mode": 420, "size": 1177}, "lib/main.js": {"checkedAt": 1752045719130, "integrity": "sha512-fbOYydx4uofs7ClQmVi9Dtluzi8nSTnsPm+OjkJReJtaB2meiMyuqbotd8HipTuxrpvumop+u10kcS54FWZfDQ==", "mode": 420, "size": 1727}, "package.json": {"checkedAt": 1752045719132, "integrity": "sha512-1lZqDmCkUcUPKAJcDWuqqBNOI+1vJFSgk2EJLVS8vSM+t/BXLHPI8vY2YVSWlTkeFtG/iBhhcKTG2nIOIZgm2g==", "mode": 420, "size": 1967}, "README.md": {"checkedAt": 1752045719133, "integrity": "sha512-ilDxxgNUwbcyrlBfAwV2sw+jmax2wtl19RKU/yNQD7bj404CJujU8EJX/B+P2l0QIofQ32yxw4OhYLOJ2bd+mA==", "mode": 420, "size": 11422}, "docs/types/index.d.ts": {"checkedAt": 1752045719133, "integrity": "sha512-SnIwZTuEwYv6lcc77WWfSBLca3drBKXhLH1i3Fel22A+Hi0ZAupXpkOQLGgAtYuwBDMBJRSc0d9fMArWtUKsNA==", "mode": 420, "size": 1106}, "docs/types/test.ts": {"checkedAt": 1752045719133, "integrity": "sha512-uoeikU4bOFBsAU6W8YEDHgIvLtI3ys0oxI2rRWRI29ApnGacDYssRSYjjomxBVA81oEm9hslGc6YlECVxE0C/A==", "mode": 420, "size": 1292}, "docs/repl.txt": {"checkedAt": 1752045719134, "integrity": "sha512-Lv6e29/nnOWKbaMPPL0pLtHxmr3OfkBv3AJ2wucJ2BQ3R0OfY6sQK6Uf7BRJt/wbHi6oq8qUogmPizeqRSubxA==", "mode": 420, "size": 552}}}