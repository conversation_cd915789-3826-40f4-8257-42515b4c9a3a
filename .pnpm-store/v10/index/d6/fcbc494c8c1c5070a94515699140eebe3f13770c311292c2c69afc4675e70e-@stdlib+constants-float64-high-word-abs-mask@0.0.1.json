{"name": "@stdlib/constants-float64-high-word-abs-mask", "version": "0.0.1", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1752045720753, "integrity": "sha512-J7jAJS6uUMo84Cq3xWcGZMDIJOA+s9oQifPwoA0j5kipVry59TZFxteWdKh8TMhtEIXcM1kRvgIQ1pEzaxIYVw==", "mode": 420, "size": 10174}, "NOTICE": {"checkedAt": 1752045720753, "integrity": "sha512-4cuPRRYzgfVMbZIC790aV9oIoAEVC+4xyLwcB73N6IHP34DxsSH2gT61hzrQQsrrPKyZ3lwkqLKxnRVCIg2vCw==", "mode": 420, "size": 44}, "include/stdlib/constants/float64/high_word_abs_mask.h": {"checkedAt": 1752045720754, "integrity": "sha512-CNUVGYjSwt7QZ4plFdDUNrqIhbjwA57hmtHcRWcFFZYOHtAEW2yMfprs22+rJRc6O85NX2yKNJYBuUNHakE7iw==", "mode": 420, "size": 957}, "lib/index.js": {"checkedAt": 1752045720754, "integrity": "sha512-cnRteutzd3AiQ4DcosIpiL8zRzN2hPbrzvc8HdqyQJSBikTFpH0xTO8oKrIBciaxOh+KxqtwyA2yvVUJFEVr3Q==", "mode": 420, "size": 1591}, "manifest.json": {"checkedAt": 1752045720754, "integrity": "sha512-0GXmtOUquUPeI1Ts2GeVv30m3SBcS90QSryHb4PwtcQBnvyNDf4uU+DfSiozU17w+rYWbqRS/n21SUDCVtQ4xw==", "mode": 420, "size": 543}, "package.json": {"checkedAt": 1752045720754, "integrity": "sha512-F/3BCzgEryBscjmXMUN+R8eaSbdJx8dYHljIbmzuev1aRJI8MUfPEFpsx34TwvkiayH4hc08+32C+5fhQXahOw==", "mode": 420, "size": 1995}, "README.md": {"checkedAt": 1752045720755, "integrity": "sha512-t4r/HJb/s7OPrh9/dR8NBAZTsw4xgl3GW3Tb2K0coED8eZBwMrKeg2vBZ2Vm5Jxk6MzleIo4d/Mizd9/5LDmJA==", "mode": 420, "size": 6519}, "docs/types/index.d.ts": {"checkedAt": 1752045720755, "integrity": "sha512-T2vyymjTvSQcw4/GVXkn5ChoMQlaebCy5lcpHtCiFM0cvRkbqbCfmVOsUyAzTkk2rt95bXHibpwutZWolKXpdQ==", "mode": 420, "size": 923}, "docs/types/test.ts": {"checkedAt": 1752045720755, "integrity": "sha512-YJ6aJo1Zvp1/cfILj6Ykj0HYaDSWmhE7BQlOZTMRcMj0aoYfvUwzvXSU1qIVJuMSxgdWY2GBAohuoTAPl9IxkQ==", "mode": 420, "size": 824}, "docs/repl.txt": {"checkedAt": 1752045720756, "integrity": "sha512-Zn1m6BAUaFFa4dIFlhvSrLe7FMMIveKmef8n08RG/FjUuFDfDX9nMfQsE1yzo95f/EBrhOWolPcKzwMVnx5hAQ==", "mode": 420, "size": 305}}}