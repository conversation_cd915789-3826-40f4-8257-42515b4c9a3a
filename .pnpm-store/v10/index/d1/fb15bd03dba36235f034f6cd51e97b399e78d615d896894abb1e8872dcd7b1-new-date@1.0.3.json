{"name": "new-date", "version": "1.0.3", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1752045716324, "integrity": "sha512-3C32aLlRG0ExIXekepIN4+jztbKmLEVkHcz3TKNvTia9V1W0Pj9WPGpo28+tLXzMSduuraI1S1wdPqhvHMyCGQ==", "mode": 420, "size": 1061}, "Makefile": {"checkedAt": 1752045716324, "integrity": "sha512-RTjQqlajXpfxSNtUois5gjKVNBukBa0WZc7a1HLGv5UugoErqnpm22VTjO0LrBVCghl19bleUnHo/mA3fDAJqw==", "mode": 420, "size": 476}, "lib/index.js": {"checkedAt": 1752045716326, "integrity": "sha512-Xc2/OoGH0Ibd2WKT9H3lxYEC8GsIqPX/yQyiRYZ+1dpEoXtxrbzT+9kcii0zj2JFKnhHAezWa8N3IaiLPJ6gtA==", "mode": 420, "size": 1181}, "test/index.test.js": {"checkedAt": 1752045716327, "integrity": "sha512-HkolXZTkKrUj5rpaA7HpLDjiFVseNBE2oyQsTnDU/lZQgwnlI+8t8JyolHAmsSGAUIG1ZnAL9CeZxw69+Ehnkg==", "mode": 420, "size": 2397}, "karma.conf.ci.js": {"checkedAt": 1752045716341, "integrity": "sha512-BFVgUI7+t4Zvz4cPnwebKNpQBU3NpjzU9SWVuPiSe9PEtO0mY+OCMHA3SEClFgR7oi9m+Y2f4gME9KLSZ2WhOw==", "mode": 420, "size": 1974}, "karma.conf.js": {"checkedAt": 1752045716341, "integrity": "sha512-+oFB0hiePZSo+FrkotN4/DT0uWT9eSN9OQ4EsM2J9daqdz5bwBlbCOi6TpzJaxzPmgFzJzrhtYMDCi9vmayF1w==", "mode": 420, "size": 757}, "lib/milliseconds.js": {"checkedAt": 1752045716341, "integrity": "sha512-FJ3dgVaqsrPcUbNIaw8w+kaJRnNGkbP/qKqwKX1pTh9GX7UWtsqdGExY1GVKVPUxUYTQFghmsmd0oFhHTZXAuw==", "mode": 420, "size": 440}, "lib/seconds.js": {"checkedAt": 1752045716342, "integrity": "sha512-DK5ujU7yUCTiaBv+MF16VViXJQ5KZya761VknXGwn5yRHv3/8yI4WlAs8qqRVtPQPr+FkeEjCSOeSIPJgGzFSA==", "mode": 420, "size": 444}, "package.json": {"checkedAt": 1752045716342, "integrity": "sha512-ZOwPCSMLItAuBLcMSDwFvewqHUv4AcVIXKuTgycQqq41Oqu8UJd+dKhEyd018s+OEEkFEQhGfybATnZRjLaMsg==", "mode": 420, "size": 719}, "HISTORY.md": {"checkedAt": 1752045716342, "integrity": "sha512-hUspi6WOHZNDGQknhRemPxXRPaPFuhtxPspbIlTDPjVFEmM9ZQs5T2BpVB2KAOctJXgwEW1lLpYRMKDl6U9DHA==", "mode": 420, "size": 1200}, "README.md": {"checkedAt": 1752045716347, "integrity": "sha512-GNdGYPoDF25TdMCVntjdv3ffF6q9nL3TCGaC9PnlFA/vug2oCF6SQDn6jNSgKhVuS/z1Jh6Xxtn/nRVUGXUAJw==", "mode": 420, "size": 1027}, ".github/workflows/node-js.yml": {"checkedAt": 1752045716350, "integrity": "sha512-rn+BishHLPQaIsrWsB9p0HjL7Q7kdefoMzME7zVYjFP1mxeaHCF7i9R4YzJxVA0wjk1zUkQVXIPZMjoiwnWu8g==", "mode": 420, "size": 623}, ".github/workflows/publish.yml": {"checkedAt": 1752045716351, "integrity": "sha512-T9rKVpjznBG0PKCHbqQhVpr59MkvBuMOfk//QmC8aFMHuGwstHZtRb/0Lc3bvPubK/zedfj9w3hCBH2YkBHaoQ==", "mode": 420, "size": 529}}}