{"name": "@stdlib/array-float64", "version": "0.0.6", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1752045717051, "integrity": "sha512-giZYqp2QbwsOHNySg9FuTpE8Yyro5HyjpXkP9Ck5aqbhCvG1sKLbnlYjh5mR2+1rIt/vv9FN8ffyrszTfwbkMg==", "mode": 420, "size": 23963}, "NOTICE": {"checkedAt": 1752045721307, "integrity": "sha512-4cuPRRYzgfVMbZIC790aV9oIoAEVC+4xyLwcB73N6IHP34DxsSH2gT61hzrQQsrrPKyZ3lwkqLKxnRVCIg2vCw==", "mode": 420, "size": 44}, "lib/float64array.js": {"checkedAt": 1752045721307, "integrity": "sha512-kBrASyhf6nH7DhwKrKGWJvkCehtEhEBEOnRtFLr85aUWFlzBIrFbtT3X7KuEBvTnqwQypDWdHJMEggATwB6YeA==", "mode": 420, "size": 806}, "lib/index.js": {"checkedAt": 1752045721308, "integrity": "sha512-btJzPFKiFRQjuGWCp78i5WcWPCwgLGiSk+wj0chTzzaQcjhh3nKcSHXsFcXS5wRyw0Aur9A4D5qtDV8OaCzt7A==", "mode": 420, "size": 1273}, "lib/polyfill.js": {"checkedAt": 1752045721308, "integrity": "sha512-WFZEIMOOAP9UFwBu21G3JmyK7oRDAdvCoOQ3rYnDspQ22Ji7QrobEoZ8OxfSHVBYOEm4ncnZUre95iORsc/V5w==", "mode": 420, "size": 931}, "package.json": {"checkedAt": 1752045721308, "integrity": "sha512-SczZJwJYiYaapzGQPzqI80nei6L1G7LUPrHgtyafRTbxB2etYhHHtZsUSRee9CYv+DqDP9KWgRBzbDwnxo3PtQ==", "mode": 420, "size": 2202}, "README.md": {"checkedAt": 1752045721308, "integrity": "sha512-3gDt3txGJ2EMmsg+T5AniOhpF6WTN286OKpOrkadVV6ynJBfV13pOxn4xXbQXjULzcIAhWoTO5N8B+2iqFpCCA==", "mode": 420, "size": 36585}, "docs/types/index.d.ts": {"checkedAt": 1752045721309, "integrity": "sha512-z5KWyTdEXF/ouDN0/9+/BdY74uuuWy8WW6sU+xL7eHF2M/m8nrmHQ05dSz0DGfQm8nhruf4MRfaYXsCsx41/tg==", "mode": 420, "size": 837}, "docs/types/test.ts": {"checkedAt": 1752045721309, "integrity": "sha512-A78VZEGdFiIam1PS1NtW1tUnQnjyC3bFefIZfZyizqX3RhO3r3HagSJgjA6w/KFq1BopRyBAWN2veSerZ2rAdg==", "mode": 420, "size": 1088}, "docs/repl.txt": {"checkedAt": 1752045721312, "integrity": "sha512-1DxkMYkXBfSKPSHelO0tt4uDmPu1F39enBjlybhcfu2st8vTMNxA8+F6EE54/OuONtfWYwTit/iAsZtfYX0MEw==", "mode": 420, "size": 23205}}}