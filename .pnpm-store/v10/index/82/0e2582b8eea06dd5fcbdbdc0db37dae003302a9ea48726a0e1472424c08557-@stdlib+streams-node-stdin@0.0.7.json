{"name": "@stdlib/streams-node-stdin", "version": "0.0.7", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1752045719980, "integrity": "sha512-giZYqp2QbwsOHNySg9FuTpE8Yyro5HyjpXkP9Ck5aqbhCvG1sKLbnlYjh5mR2+1rIt/vv9FN8ffyrszTfwbkMg==", "mode": 420, "size": 23963}, "NOTICE": {"checkedAt": 1752045719980, "integrity": "sha512-4cuPRRYzgfVMbZIC790aV9oIoAEVC+4xyLwcB73N6IHP34DxsSH2gT61hzrQQsrrPKyZ3lwkqLKxnRVCIg2vCw==", "mode": 420, "size": 44}, "lib/index.js": {"checkedAt": 1752045719980, "integrity": "sha512-P1ZW+11fvW1G8n2b21al8eX+7Rs/A8ZsL3eTeWhqzEJGEBFqBRCkBg0zPIQalt4bKwVla21+OpZ7VTy0y9e9SQ==", "mode": 420, "size": 960}, "lib/stdin.js": {"checkedAt": 1752045719981, "integrity": "sha512-MjcMEuDFps9fKuMlORDDgq5iXGugtNTkrWp41rlJRRVvJU8e/dYETJ4UbtyCO9FiXmYQjC/8sFQzJwm4+zqu0A==", "mode": 420, "size": 727}, "package.json": {"checkedAt": 1752045719981, "integrity": "sha512-lwiRUgqTwBNlGATPc99lBBuJwzuQjF6Lw2gsXNLdyowqzVessdMgljSXFbUqplSKrO25oCOxojMhOC99cjw+cA==", "mode": 420, "size": 1528}, "README.md": {"checkedAt": 1752045719981, "integrity": "sha512-jFN+dHthkCWwkFr98M+e7TTQWm5SG3zS7Z80gweolxLZ18WBOuHyhO0ueVERbBVsFFQFVD9URqgJcCsrhAMT/A==", "mode": 420, "size": 5279}, "docs/types/index.d.ts": {"checkedAt": 1752045719981, "integrity": "sha512-aD6jre4CJzSbjuEVPIPQ2jALc8xHAPty7yd7barYTuN+kr6nB5nXiFFywFcXWFn+0tiz6M4XVJcNmC/AOrOSXg==", "mode": 420, "size": 767}, "docs/types/test.ts": {"checkedAt": 1752045719981, "integrity": "sha512-NyoIB0rP1uYCGOfCpFXV6hIAIWg8dNH4h7yh5fXFMaEmAa9JCfP9EEgo5PoNo2UZ+8RsJ66NaHX150zTe+WoVQ==", "mode": 420, "size": 797}}}