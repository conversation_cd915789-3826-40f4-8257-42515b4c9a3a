{"name": "@stdlib/constants-float64-high-word-sign-mask", "version": "0.0.1", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1752045720877, "integrity": "sha512-J7jAJS6uUMo84Cq3xWcGZMDIJOA+s9oQifPwoA0j5kipVry59TZFxteWdKh8TMhtEIXcM1kRvgIQ1pEzaxIYVw==", "mode": 420, "size": 10174}, "NOTICE": {"checkedAt": 1752045720877, "integrity": "sha512-4cuPRRYzgfVMbZIC790aV9oIoAEVC+4xyLwcB73N6IHP34DxsSH2gT61hzrQQsrrPKyZ3lwkqLKxnRVCIg2vCw==", "mode": 420, "size": 44}, "include/stdlib/constants/float64/high_word_sign_mask.h": {"checkedAt": 1752045720884, "integrity": "sha512-7t96bOjgNDGMeY2bFdEUOADJwWY1fHSA/oM7YJlCDkTzwrH8ZQqFy2u/9+yZt54PEGSMmvbHJK9KvSXaPRCdiQ==", "mode": 420, "size": 951}, "lib/index.js": {"checkedAt": 1752045720885, "integrity": "sha512-UXdp+KOQiagqzn26iISjbE30qNb+JE1HB5p5pHGMGC08xnM9zsWlEOSoLWI532WnAKH9bmv1qDiJTBlO7r4iLQ==", "mode": 420, "size": 1566}, "manifest.json": {"checkedAt": 1752045720885, "integrity": "sha512-0GXmtOUquUPeI1Ts2GeVv30m3SBcS90QSryHb4PwtcQBnvyNDf4uU+DfSiozU17w+rYWbqRS/n21SUDCVtQ4xw==", "mode": 420, "size": 543}, "package.json": {"checkedAt": 1752045720885, "integrity": "sha512-t6aNwmW//5RPc7L/53UkSVe4c1P30ZvQej5KQNfm0GScJ5nUzxjukyLvyOcgMipm68F4EP46oa4gV32DPcDCNA==", "mode": 420, "size": 1987}, "README.md": {"checkedAt": 1752045720890, "integrity": "sha512-33DDR0pXVYjk73zhecA18YHui748PGndtXAh1EM8LIDnz7dPoi0fMQCYo+glSdOsw6u/I3d7DtRlajoUKBk3qw==", "mode": 420, "size": 6669}, "docs/types/index.d.ts": {"checkedAt": 1752045720890, "integrity": "sha512-ocG1Cx510g9KdJjc8SKiG+9DHz3LAgG7mOc47UIt6loRX5clKYiFObyCOK5fP8cvLwhLaE9yC3CdC0g+fq9B5w==", "mode": 420, "size": 916}, "docs/types/test.ts": {"checkedAt": 1752045720890, "integrity": "sha512-CPxmlDwzDrGon3PTyaAz3t2RbYwRMbdHZoJStadV2zsqYf7p3Y0vEunFXgozotAyzUpxJScaGtoCjhv8ss0y9Q==", "mode": 420, "size": 826}, "docs/repl.txt": {"checkedAt": 1752045720890, "integrity": "sha512-l9u28wrvCinVN9/XdpB2HVd6FJK7wUsm5g+/nfVWmDt5bfRUFF/QTdTRQBkiBRciiy7FNLvubSc/GJhtTARGlA==", "mode": 420, "size": 290}}}