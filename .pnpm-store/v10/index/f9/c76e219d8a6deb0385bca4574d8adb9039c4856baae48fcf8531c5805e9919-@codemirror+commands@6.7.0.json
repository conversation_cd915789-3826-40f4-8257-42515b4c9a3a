{"name": "@codemirror/commands", "version": "6.7.0", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1752045734185, "integrity": "sha512-93AnA94LXftw4iulmPgONmsxHTOmEXXVnuDgrubQugXHDb6+kqBYV3LU04W4nfNUBsUNOpHH1CYTncof2riVDg==", "mode": 420, "size": 1118}, "dist/index.cjs": {"checkedAt": 1752045734191, "integrity": "sha512-ZuH4+yEtrtDYlimncDoTT69Tc3GSmYX0YOXZzszuDX7hmuW1XzkA04WUJVmSzIIJYPUFrS64mg5L4ixE5NqLYQ==", "mode": 420, "size": 83047}, "dist/index.d.cts": {"checkedAt": 1752045734192, "integrity": "sha512-aWly9nEe1FhZ/wcJgYNJEqziYrV4MamQ5qIaemiDWePvp9BJqdJToRbczTrv568xl9xTIpFds4PiInKqSEiy+w==", "mode": 420, "size": 27853}, "dist/index.js": {"checkedAt": 1752045734193, "integrity": "sha512-I9DZWpZqiM7OjJ4LAvpanK5zhz5yC8ixJrd8LARgkPxhuiXjFCosZMs7eglIo7ZIueyqbl5hQkvTMgp215r3+Q==", "mode": 420, "size": 79768}, "package.json": {"checkedAt": 1752045734195, "integrity": "sha512-9emZLNJw6hBwHboq7VAnRuiJPy2V/inx+/hL0id/SyF0KQcbRgC0eiSiG2HlvlNvdANAYGHEgKsZ4WE/teuZuQ==", "mode": 420, "size": 1024}, "CHANGELOG.md": {"checkedAt": 1752045734196, "integrity": "sha512-b8qHct5pF9fp4+Hl98QCJFs+EgVfLdhNaRCGmmJ40611JK/VuxOgyCSKXTTYP2lEm6BHTScBZJR8Kc9nE0BRfA==", "mode": 420, "size": 9627}, "README.md": {"checkedAt": 1752045734197, "integrity": "sha512-J9mTX6nJrX3MnljxDKcDXLLpQqm+Ni8IIVvRcdkTQv2ZNXxg1Zzl+ENYQgc5Zvk6hdV4bY0Q7QZqkf1BQ9C2PA==", "mode": 420, "size": 1038}, "dist/index.d.ts": {"checkedAt": 1752045734192, "integrity": "sha512-aWly9nEe1FhZ/wcJgYNJEqziYrV4MamQ5qIaemiDWePvp9BJqdJToRbczTrv568xl9xTIpFds4PiInKqSEiy+w==", "mode": 420, "size": 27853}, ".github/workflows/dispatch.yml": {"checkedAt": 1752045734201, "integrity": "sha512-m1oo9FDcj8HyBMn9j8PAdgJGReOxejwknSt0WhQByhphBMTHHlS1n9+UPCgcv631fi+EwCNIv0+z6VZAl52zag==", "mode": 420, "size": 415}}}