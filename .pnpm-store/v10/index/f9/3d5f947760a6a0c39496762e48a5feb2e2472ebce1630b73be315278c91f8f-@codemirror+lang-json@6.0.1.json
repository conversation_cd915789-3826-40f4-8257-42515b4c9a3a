{"name": "@codemirror/lang-json", "version": "6.0.1", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1752045733780, "integrity": "sha512-lw5+YMNaSRogD0pcG34qPVM97SHZai6rc73STfIed1yN+CWR1TZ5ZA4Eo8vF3a+WLGtFcUA/HikBJ2nFdwA60A==", "mode": 420, "size": 1112}, "dist/index.cjs": {"checkedAt": 1752045733780, "integrity": "sha512-T20zRtw1lgwbTFyNcieSRTREtyDu95PZs08FKhSuQDwdgjq88fz7BoISkYq1Gt/JDNTPG4DtAOCU3JOi7daCrQ==", "mode": 420, "size": 1920}, "dist/index.js": {"checkedAt": 1752045733780, "integrity": "sha512-GEnQ/4+OaauANNmxozrhyxO1W3z0KdHawxNit/Dm2/vrSOj05eKTKJzQvKY/mHbFMZoGPLm3Uj7TBT3oCGVRhQ==", "mode": 420, "size": 1876}, "package.json": {"checkedAt": 1752045733781, "integrity": "sha512-92iRneaHbgxS2LU0sofGKKmFmFuMZeCF2aqyKtKPavPsZjBidg/mAwb2WDYVAmz5Lcky+6edOE8Q7U6CwzmO7A==", "mode": 420, "size": 890}, "CHANGELOG.md": {"checkedAt": 1752045733781, "integrity": "sha512-zNRaK83M/QfYdMQSmxK4FHbwe14aVqRZTymTMCWrc4V/j3xhXOHzFBzwUROS0JCXk5DUuw9ru0keqhL3dzziaw==", "mode": 420, "size": 711}, "README.md": {"checkedAt": 1752045733783, "integrity": "sha512-RfpjA/oQ1NQcg0jvjl4P3Nv0jLEgXyCyWmj77HUxvPWC9eVexeulUFSLDRRVcgluuDo7oa3V8PQjUE53NydjPQ==", "mode": 420, "size": 2225}, "dist/index.d.ts": {"checkedAt": 1752045733783, "integrity": "sha512-/+h2wl4LVV558sOV7WYzLUPc9uMsXaWBTP3+P+KqkkK8LjZzfpkW6oo2IbMqvDjadTxkPwuvrrl9S4N6LsTwag==", "mode": 420, "size": 655}, ".github/workflows/dispatch.yml": {"checkedAt": 1752045733784, "integrity": "sha512-m1oo9FDcj8HyBMn9j8PAdgJGReOxejwknSt0WhQByhphBMTHHlS1n9+UPCgcv631fi+EwCNIv0+z6VZAl52zag==", "mode": 420, "size": 415}}}