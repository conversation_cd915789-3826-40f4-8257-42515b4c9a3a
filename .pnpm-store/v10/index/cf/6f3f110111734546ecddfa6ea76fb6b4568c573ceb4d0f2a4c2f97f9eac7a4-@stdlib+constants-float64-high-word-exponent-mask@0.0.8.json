{"name": "@stdlib/constants-float64-high-word-exponent-mask", "version": "0.0.8", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1752045744799, "integrity": "sha512-J7jAJS6uUMo84Cq3xWcGZMDIJOA+s9oQifPwoA0j5kipVry59TZFxteWdKh8TMhtEIXcM1kRvgIQ1pEzaxIYVw==", "mode": 420, "size": 10174}, "NOTICE": {"checkedAt": 1752045744799, "integrity": "sha512-4cuPRRYzgfVMbZIC790aV9oIoAEVC+4xyLwcB73N6IHP34DxsSH2gT61hzrQQsrrPKyZ3lwkqLKxnRVCIg2vCw==", "mode": 420, "size": 44}, "include/stdlib/constants/float64/high_word_exponent_mask.h": {"checkedAt": 1752045744799, "integrity": "sha512-9esuol2XeZGVm3HstQSZ26MadRjlziLfdcuBBwKxntrNVIQcQ+GfRmKwt0O7VmOOUuPvwv1LBIRP4QymIkvuMQ==", "mode": 420, "size": 967}, "lib/index.js": {"checkedAt": 1752045744799, "integrity": "sha512-xnTWjKCPeliW7h07+C25k2Ph86oTSylNF7blsx9UItjpvn1Q7pL9NLMzTVwrbZhAExsLmQHa4IQyHwQxzkxKOQ==", "mode": 420, "size": 1549}, "manifest.json": {"checkedAt": 1752045744800, "integrity": "sha512-0GXmtOUquUPeI1Ts2GeVv30m3SBcS90QSryHb4PwtcQBnvyNDf4uU+DfSiozU17w+rYWbqRS/n21SUDCVtQ4xw==", "mode": 420, "size": 543}, "package.json": {"checkedAt": 1752045744800, "integrity": "sha512-Kd1Oxu4q5b+oXTQEF2oqviUiOv47Qcn+U1T8rQ25fIg8ka961/XrKu2x26psmbBTcHGkk2EBbFlCbkGfVlc1CA==", "mode": 420, "size": 1885}, "README.md": {"checkedAt": 1752045744800, "integrity": "sha512-2I6Nbo4m4XR3AQqE1TGk+sfCBt6Z4DumF3UBdzZMzG/XP2VXZrhkc8flL5clApkqvUafTs0JNmX5RVv7zAscBA==", "mode": 420, "size": 7052}, "docs/types/index.d.ts": {"checkedAt": 1752045744800, "integrity": "sha512-iDoRxnPaglrkoXk4yOO+dx4JNmC6s6D6ZS5RhUGLw9gyjzUMYNqmYUzV+6A5tsULSKFVsVrtFnYsR0EzgQRnzQ==", "mode": 420, "size": 928}, "docs/types/test.ts": {"checkedAt": 1752045744800, "integrity": "sha512-MuErmcBdQ8OuGSmb6ZeSw+F6EQDb+SI2yhIY6fmCCrHwf6Z8nSxcDnkF5C/yHuMAabnqj+yazMGE/tUzIxKcxQ==", "mode": 420, "size": 834}, "docs/repl.txt": {"checkedAt": 1752045744817, "integrity": "sha512-EG+kwrCDvHiY79L6p7UFIV/9McdNeVi5TpI1gq/yk5Mj4dChreAJJk1w1n99WedbMx1F+OBnQlGOTv8cYDEAtw==", "mode": 420, "size": 290}}}