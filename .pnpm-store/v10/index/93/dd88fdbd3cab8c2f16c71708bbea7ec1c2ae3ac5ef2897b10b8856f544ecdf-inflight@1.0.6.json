{"name": "inflight", "version": "1.0.6", "requiresBuild": false, "files": {"package.json": {"checkedAt": 1752045737161, "integrity": "sha512-+lzFHlpGtb6OlTiBKtGnGXksf3tEJiEKhA0d8Y2FRS0eJ7KmTsewFJ1eph/+Dif9AxnITAzUlFy6prTn9m5UXw==", "mode": 420, "size": 658}, "README.md": {"checkedAt": 1752045737161, "integrity": "sha512-DRhs3k2wprwVcVIPnFPPRRD5xG2V2HA1fxuEO3KiNAouExP2qZ5xxpFIf+4HFifCLtH7g07m5bss3cN5oc5dQQ==", "mode": 420, "size": 991}, "LICENSE": {"checkedAt": 1752045737161, "integrity": "sha512-ydYWK++YgKWralr+lvPsG9nerXWMpCf5ui6OnZrar1ZJqtlC9pjzm3qaQ3mE+NwJFB84NM14sDEE+BrZCNFbMQ==", "mode": 420, "size": 748}, "inflight.js": {"checkedAt": 1752045737164, "integrity": "sha512-G4Mq5u8S3zidxQKMzYDcgR6rXUgW24e6jlMepHogl6JoAcFPFeDnNslMZllk0j5V5YdQ/P92kxZrKtsffVgsiQ==", "mode": 420, "size": 1365}}}