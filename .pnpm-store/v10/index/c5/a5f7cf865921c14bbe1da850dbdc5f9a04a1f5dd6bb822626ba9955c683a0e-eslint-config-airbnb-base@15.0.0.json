{"name": "eslint-config-airbnb-base", "version": "15.0.0", "requiresBuild": false, "files": {".babelrc": {"checkedAt": 1752045738995, "integrity": "sha512-B2IvJkG0QMij1CDBGbZ3U/yY3Uzb6aANWzv6hRtBV9rwDKkPY6WEC3Vkfnti3W+Y/Yh6y21iAl22P/AQi4Bsrw==", "mode": 420, "size": 28}, ".eslintrc": {"checkedAt": 1752045738995, "integrity": "sha512-T6fWrpaPJDyCA9txpjJzjjJIIAtL7EjQ50a9MGE9EZBzQHeJhsHw0q4Aa6VriVwEbfLjhjRXrWQE4mmkFvVsgg==", "mode": 420, "size": 245}, "test/.eslintrc": {"checkedAt": 1752045738996, "integrity": "sha512-CZ721MeZsT2Sr2Igga5rR6gi0y9ZKZk3R8lRdsfFkQxouI7UUFOqp3S04GB9wy0poESebFbBJIPt/IwsMN+eIw==", "mode": 420, "size": 233}, "rules/best-practices.js": {"checkedAt": 1752045738997, "integrity": "sha512-Ktez/NOzWm4zKa1hd099n2/TWkR0o6KCiUqOGfcz3nnBpx5C92lZFTe7OLa6EZulfLtzMtxfK5KHA+EOYKekxA==", "mode": 420, "size": 13950}, "rules/errors.js": {"checkedAt": 1752045738997, "integrity": "sha512-jKPwqhLVfzcN3H5QVfIIArxPWZsFKl8lF2IEfToBAwpjBGY7wXghMukNOdWJAaLhrEXB9q/MPmWgzeGFCGXkrA==", "mode": 420, "size": 6544}, "rules/es6.js": {"checkedAt": 1752045738998, "integrity": "sha512-gVSfOD4iJhMduXEQJk8zLpj2ek/pKJBa/YnISdO+TeE+Guy0JFHN9fQAfN0qH+1IW8zf1ufALDEqh7HJXD6IOQ==", "mode": 420, "size": 6172}, "rules/imports.js": {"checkedAt": 1752045738998, "integrity": "sha512-ROKwk+WPCiaepk9oW81du3Jdl6MC+WuTXy5uLQzjLRGM3yLqZ1g8BEEpyXqrs6zP24wx8eZCR0KGMct7Ebsu5g==", "mode": 420, "size": 12165}, "index.js": {"checkedAt": 1752045739005, "integrity": "sha512-AtttSEsXfOz9gbvNnkilZDGiIhN2Oj2gIpSXVinRncjVR8FCsec10t9APzXdoJi7kOmn+WJs9OCmVtedR5LY1Q==", "mode": 420, "size": 329}, "legacy.js": {"checkedAt": 1752045739006, "integrity": "sha512-8PSB/mra6pOSO2azeSG9jfDSPOdoufK9Whpv4mpSc+F/eM9cXdD/6+oZ6UbNeXmofUV11xuqm2soIdD0A7VtpQ==", "mode": 420, "size": 824}, "rules/node.js": {"checkedAt": 1752045739006, "integrity": "sha512-mzk3mWY/fmUHwe6IVuqzfOtCi8E6FGNx5fO9+Jtboyp+UnRX6o5PIHyBaDAmPUWeTHfRJ2PSiNfQ7KMvhs0Cjg==", "mode": 420, "size": 1152}, "test/requires.js": {"checkedAt": 1752045739006, "integrity": "sha512-<PERSON><PERSON>jkIQbCKP+ubnGLL1w5sSXvQze5gBTpa9LJNqaFYqE0zw2Sh7t3QGoBXUFNmmEa1wfAeHIE0rmGexFUeVP5Qg==", "mode": 420, "size": 358}, "rules/strict.js": {"checkedAt": 1752045739007, "integrity": "sha512-yu5sjmitYLvNkJHLcyvD9NiXK81Xt5IT4mxTEXYzyVSyctcpUT1ahmhdKWDhhhCPR3ZruCVvlTrHAhzzV2LbkA==", "mode": 420, "size": 112}, "rules/style.js": {"checkedAt": 1752045739007, "integrity": "sha512-Dtbg+jUwmixqRFCQW7d0k2Whyh9JE9B5aUgGkq3FdoJg3A/7jMfcRLHm5248SeF7CHFvvjOC+JqpRDFJm2NxsQ==", "mode": 420, "size": 18642}, "test/test-base.js": {"checkedAt": 1752045739015, "integrity": "sha512-tX6rbBUR81drWqQ6ODaTL9AQpaqXt0mXiVC1EDONd46PjcismheCI3CKIvM4bTV+YXD66omS34fy3FcbWHvH+w==", "mode": 420, "size": 1049}, "rules/variables.js": {"checkedAt": 1752045739016, "integrity": "sha512-S38uDDmfLQzWnAfmnXIdbbtFUx5DbO0l6z4WIoMdWzimgpjyOOoyQ9obTbG06Awn2I+lDgD+TRmwkqyB2Mnklw==", "mode": 420, "size": 1841}, "whitespace-async.js": {"checkedAt": 1752045739016, "integrity": "sha512-cUEZ677VcTqLxN4C+GGuIgjU6JtCoKeMPZrI/ffPurklIC1/ssiaQYJeOSDf6CcsV9b1HyTMvPfUX+eOq9opHw==", "mode": 493, "size": 2484}, "whitespace.js": {"checkedAt": 1752045739026, "integrity": "sha512-OW<PERSON>usPSon7T5DmDAxZ7Mn7kOXd+8fCX3hvbI6ANXWYWhg0pn2PPoXv3DYwLPWKifhNOphcbfPJRAjlZNHyzj4g==", "mode": 420, "size": 2843}, "package.json": {"checkedAt": 1752045739027, "integrity": "sha512-kTNm2eprEtcNT1O4dEL32gqKAB5z9Ez9x93ZjAa6g1djB1y3q3l2iBYLwF0LkJ6iveImXbWDhKosfTMLxqKQlA==", "mode": 420, "size": 2674}, "CHANGELOG.md": {"checkedAt": 1752045739027, "integrity": "sha512-y5xZR2z+9Qozg9Rxpsi/bf8mv3EXiMRs8jXGR6sK19kYKWDbRclBdmnyJ8ddXq9wPwh+jOZHhTR+qhOcX3fqnw==", "mode": 420, "size": 16928}, "LICENSE.md": {"checkedAt": 1752045739027, "integrity": "sha512-TzEytshVmcqyQwSLD2BuyLRSJ/g2e3ZVWNFHX5ykcs7a2j17dX8CXC8BNVJ3PsBrU8R/cdxLmyc7cmHPv5g2uA==", "mode": 420, "size": 1063}, "README.md": {"checkedAt": 1752045739048, "integrity": "sha512-WwbOr1TZToECfMmOgVkiG91/N5adM2yANIMpFnzwY5vFhuk29G7AukqTOqyiw2i5vBC3wFH3wZzUw2s/gst30g==", "mode": 420, "size": 4392}}}