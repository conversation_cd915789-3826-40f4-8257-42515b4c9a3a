{"name": "sade", "version": "1.8.1", "requiresBuild": false, "files": {"license": {"checkedAt": 1752045736178, "integrity": "sha512-ZkoPlLLLNE+x7uYhT2lFNl+mwCMeZeFOJcAZPO7exJwgOsqOVgLz1xP7U8obr3Ev63lEyJpJ72AcTI7XSYzgQg==", "mode": 420, "size": 1122}, "lib/index.js": {"checkedAt": 1752045736178, "integrity": "sha512-m7SOqqUgwdpibf6rypjg4DOcGje8uO4I4Dd7EN1DK2ItqcvqoNJ/uJqcbHluR9Y6nAGQCscmQsNlKhG0eaoZXg==", "mode": 420, "size": 4274}, "package.json": {"checkedAt": 1752045736178, "integrity": "sha512-x2Lv4SujmjRu+lR3B3i4+hqU7fJwDSGkVCN3VWRxZp1hiz1dOgcIHYH02d6yXnlQrinBe3i4vg7VcGeB78egCA==", "mode": 420, "size": 820}, "readme.md": {"checkedAt": 1752045736178, "integrity": "sha512-+y/iyXwKKJ2qvXmYwYFZa10ffsrA5bbj2FNqBH5O/0AiBEqEbheGDhSzzKTTqXQQXk2VQkWVUixNLCqpgFW9dA==", "mode": 420, "size": 20116}, "lib/index.mjs": {"checkedAt": 1752045736178, "integrity": "sha512-3NFLEogGMTq1kzyVpkirp38+qD7yVn30Bkwme32smCG1U1rNqfXE8ADmCZTmKz92MfzL2dI38hdhPicuznsDhg==", "mode": 420, "size": 4275}, "index.d.ts": {"checkedAt": 1752045736179, "integrity": "sha512-Va1hjgmhaErOe9OmUM/6vfT53DyfvMOpfJeHIVMGrgaWXIxopPAhSoC4zTEWJw03fxKqKUU5D0MUpNBCBEaHUA==", "mode": 420, "size": 938}}}