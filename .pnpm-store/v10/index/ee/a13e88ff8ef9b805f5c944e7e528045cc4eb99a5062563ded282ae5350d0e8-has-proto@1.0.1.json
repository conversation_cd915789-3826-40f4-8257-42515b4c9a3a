{"name": "has-proto", "version": "1.0.1", "requiresBuild": false, "files": {".eslintrc": {"checkedAt": 1752045740759, "integrity": "sha512-4+yheARSL/pPQeg25245ejEKIOgmGjgRW2fotkREQVMDnQQZj7Rw9FvimX0senKxW9R3GgLHQbPLwHLqbvQy6Q==", "mode": 420, "size": 43}, "LICENSE": {"checkedAt": 1752045740759, "integrity": "sha512-16Lw2sQpq8QINLGIfL/+xkhzMDKK7+1KhO0tqPzWh6SJx+JcmBuZ7w7FwLce+/bDBInYIE8pUEWvqkiN2Y1kTg==", "mode": 420, "size": 1067}, "index.js": {"checkedAt": 1752045740759, "integrity": "sha512-7WWnG52qGnSp2RaYZi7iVwqP+vAqkyA73RSO9nXkl599khfXzGR0g/jEDRtOARsuLlHEaRj4Kl2wgguslBTTqg==", "mode": 420, "size": 197}, "test/index.js": {"checkedAt": 1752045740761, "integrity": "sha512-E28qM2Pu6DSamp8ZXjrAuR+W58595TdiIisiZRU1KLk6hyUvv49HVvgGeZbPamptMZ3nI14YRlLc7xN0KCFblw==", "mode": 420, "size": 477}, "package.json": {"checkedAt": 1752045740761, "integrity": "sha512-M1davQmIzlmolA/pD1OAnmW4OS91Z5BUvqi2dwo+x6zOM7bQV7wT3UPBsUhnDFCgbLEvSWO0zYMmFqOkf5kpag==", "mode": 420, "size": 1904}, "CHANGELOG.md": {"checkedAt": 1752045740761, "integrity": "sha512-O0MBLgOWHq+HRFmJxc1fvGPAlwzq8h0gQb5eTcwEOw/aed2dF8+lQvD7rNx7WVLAr2F3bwoW6QsSmVjv4jdcfw==", "mode": 420, "size": 1304}, "README.md": {"checkedAt": 1752045740761, "integrity": "sha512-H54KK0XEkFw4tKApk7Dm6Hf2goAhVv+Tj4k+gjs0zYVrTOhWNTngsHaK2TnvZ5ne6AuiBrE+AihZAPnmrk/Xdg==", "mode": 420, "size": 1623}, ".github/FUNDING.yml": {"checkedAt": 1752045740762, "integrity": "sha512-55T45wDpjQugigL2F+WcN919ezyBZ2bui1/3/yD52ZWHl4m4gyeahCc5gCULEeiUJoPbASrsXLHIuPUxEZReuQ==", "mode": 420, "size": 580}}}