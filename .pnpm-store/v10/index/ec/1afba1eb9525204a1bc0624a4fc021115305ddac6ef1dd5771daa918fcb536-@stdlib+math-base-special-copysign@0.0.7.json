{"name": "@stdlib/math-base-special-copysign", "version": "0.0.7", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1752045720789, "integrity": "sha512-J7jAJS6uUMo84Cq3xWcGZMDIJOA+s9oQifPwoA0j5kipVry59TZFxteWdKh8TMhtEIXcM1kRvgIQ1pEzaxIYVw==", "mode": 420, "size": 10174}, "NOTICE": {"checkedAt": 1752045720789, "integrity": "sha512-4cuPRRYzgfVMbZIC790aV9oIoAEVC+4xyLwcB73N6IHP34DxsSH2gT61hzrQQsrrPKyZ3lwkqLKxnRVCIg2vCw==", "mode": 420, "size": 44}, "src/addon.c": {"checkedAt": 1752045720789, "integrity": "sha512-5ayqodCqIHbnuzel1/MUZgimnTDnDBXjFBNqQwjZBK/+VZt9wBP/mtSttymlchLEWzEUtKHU6oEbrL/dMs7Jqg==", "mode": 420, "size": 802}, "src/main.c": {"checkedAt": 1752045720790, "integrity": "sha512-hFnFZKsAabbQ6rQsM6WtyY64utwhwNgZEcON8x3Nt8rAD2/ni13vMFMhi6kHNIJJMv0lM+upRkp6I+P0F+YXTA==", "mode": 420, "size": 2039}, "include.gypi": {"checkedAt": 1752045720790, "integrity": "sha512-Oep8qSbfD6AwlnW9C6FsBv1iGNNV+e9r2p0Os4J02DW6lhM7B5ZT9uyqwb0+QfYv+xROTCaZghR6OI2HMOyiYA==", "mode": 420, "size": 2193}, "include/stdlib/math/base/special/copysign.h": {"checkedAt": 1752045720791, "integrity": "sha512-7JAY1X33wqZoo0IXUo2Spij4Y0p7uwa9ruDgO0z81nTFkgyGkMVZKgaAdsOs0uNPyLVQWEfqYM82mmx3CatVSg==", "mode": 420, "size": 1142}, "lib/index.js": {"checkedAt": 1752045720791, "integrity": "sha512-iqcn215PERzZ8Cbm9unLQTWii0r0da2xLDu6LTV+qw9buRxff5t23mC1JVP2543qQLFhN8tddm2K1J20iv1+YQ==", "mode": 420, "size": 1209}, "lib/main.js": {"checkedAt": 1752045720791, "integrity": "sha512-jJ4jjDk57VibolSGJM9ZXNEcVM+xy+NVqw7TeOSn4z2Q+nuCm9QnxBfxK0vELfB++aKobOHujyH6Kwxmun5scA==", "mode": 420, "size": 2254}, "lib/native.js": {"checkedAt": 1752045720792, "integrity": "sha512-Plixs6+Vim51e885Sd/7oq3TC7Kg9T9nSy1BmQ4aj3Lthvo0OGOaCMrg/a3DHtGCkH9LWc0BziUoWQALmV6lkA==", "mode": 420, "size": 1429}, "manifest.json": {"checkedAt": 1752045720792, "integrity": "sha512-0vufmvJYgpg8V4Gw8CbwIIoHe5Iwccj429GmlDyuNXBU7skMzQxfURJ+EOVrXO8PxpM/G5+5tfJxaRur8mUA6w==", "mode": 420, "size": 1940}, "package.json": {"checkedAt": 1752045720792, "integrity": "sha512-VOpQRP7+FBdtdqAOn9Z1qeIqplj6br3UqxrPcRuYhrGqmQtbhh6a74xqM5RH+Yc61SJRlPjAwmNctrCzC5sC9Q==", "mode": 420, "size": 2593}, "README.md": {"checkedAt": 1752045720792, "integrity": "sha512-qZMNf3S2gfXCbHLRRFjJmrHqnI2nc9D3kTBbKWYOjqMus6MSlWIyfv0LVT6RRQMj6F0EpOXgFHKykinfu6sZ6Q==", "mode": 420, "size": 7436}, "docs/types/index.d.ts": {"checkedAt": 1752045720792, "integrity": "sha512-2Yo4YiT2iqpy19PGy4RcFnJUTKe6VYRiqM0sZ1+wV5W404mSGBXA/jueNx1A6Ydk1YQ5t4bmDw1LKDq2UY0ttg==", "mode": 420, "size": 1326}, "docs/types/test.ts": {"checkedAt": 1752045720793, "integrity": "sha512-U0Fto3JOeIWkr092qmcvGmB17tQ12QekU40qSF1COa3iKF2AmyZRHCW/q8TbjRmjcAd7spWtpr3s/cDSaUNd+w==", "mode": 420, "size": 1774}, "docs/repl.txt": {"checkedAt": 1752045720793, "integrity": "sha512-UpS9TieA27ZVNOc9x+/0lD3yRzHs5G5GDRgZfyUTc1LqKjDBknvksGu75zD9v+NEimdYLxRpPxfgkKC4HNew4g==", "mode": 420, "size": 638}}}