{"name": "call-bind", "version": "1.0.2", "requiresBuild": false, "files": {".eslintignore": {"checkedAt": 1752045740500, "integrity": "sha512-VLhEcqup3IHXtZJPt07ZYoA9JNRjy1jhU/NU41Yw4E8mEyea/z+6bw5hL3lhCO09pji9E0BH2Q3aDXdc3i9zBg==", "mode": 420, "size": 10}, ".eslintrc": {"checkedAt": 1752045740500, "integrity": "sha512-W9aTOx/jwoMo1b73ksKlLj4Ja6SW2npz2mTsd/9VfAY5BpyRSdnISCTZBq60zf4RlbhmZkJFszTH0ZkvyDjyGg==", "mode": 420, "size": 247}, ".nycrc": {"checkedAt": 1752045740500, "integrity": "sha512-42sgwWzusJB1Dzhl78jX/Zg65Oi0HDDMOGXS/Uklv1kCYn4fHtRsD/JFPwdu+d40vome9XdUspzRWEQAcTGEeQ==", "mode": 420, "size": 216}, "LICENSE": {"checkedAt": 1752045740505, "integrity": "sha512-EqKjvyq6+l4/ZpJ47jMsh+ShZM/lTvYBnKi189QfOPECJNUvRx0CVqrWYA3sn7Oh7fPITzjMnxz9Trmir4oAZg==", "mode": 420, "size": 1071}, "callBound.js": {"checkedAt": 1752045740505, "integrity": "sha512-sHRsnckLtRPn1hkEcM4Ky7DCBZuXGEuTkfWB6aSQcpZpyEebqOqvwZgPTgWKOWutEQMdBIOHtSI6e0ueaksdrg==", "mode": 420, "size": 413}, "test/callBound.js": {"checkedAt": 1752045740506, "integrity": "sha512-7pwV5xIkrzm/sYG+rUZnrZRczF19plBmkjt7L6AdCG1GeZ+/dRZAjkK+O3eWIMCzO03rh9kN/uMrRr/75ULZ1A==", "mode": 420, "size": 2379}, "index.js": {"checkedAt": 1752045740508, "integrity": "sha512-+lN0lV6LR1nm1Jmg0z2/gZ+NDVWWf91cbvguCDy9PLRP7NAuTF9YPPuJ4rU4//QOjiU8r0l1DyQEcX7ikPFjwA==", "mode": 420, "size": 1306}, "test/index.js": {"checkedAt": 1752045740508, "integrity": "sha512-+44V8d83UWJOSH2lkDXAF617/9TgqHTu8y2dJx7xzvj7gs/BjkZ22QVz2MaA25+D46cwrhTAt7G7uZBm2KpgXQ==", "mode": 420, "size": 3354}, "package.json": {"checkedAt": 1752045740508, "integrity": "sha512-9Rm8ORMbrCvbf05779y5AWUUAYdXTZIG711pnJPsq6D5BhWLJyXy+m11PXqos847uMhDd7U3frHbCVPj481e2g==", "mode": 420, "size": 1793}, "CHANGELOG.md": {"checkedAt": 1752045740510, "integrity": "sha512-P08ZkWfm4TDfg+K1AlqcazWuD6LBXoQfC+Xsu4Na5o0zWLFaXEE94PJmmn7I2mbOgaq6OLn8HNaUnXarsfi+cw==", "mode": 420, "size": 3331}, "README.md": {"checkedAt": 1752045740510, "integrity": "sha512-xctUIIzrYlRfF7BAigp2hQZisE4Miq/67iym+OV1Xk/Qt0DuBG2Wvog2OQ7drBOIIFOm5xDcMf7nAD1fA+KPPQ==", "mode": 420, "size": 48}, ".github/FUNDING.yml": {"checkedAt": 1752045740510, "integrity": "sha512-ZMiaLOke56GhLQbcpNev/nMsGDpkuxH4cB0E82tlff4Lhd2UiBhSl1zkS9b5GB/k4u8TR+atMclg3xdFqNgv/A==", "mode": 420, "size": 580}}}