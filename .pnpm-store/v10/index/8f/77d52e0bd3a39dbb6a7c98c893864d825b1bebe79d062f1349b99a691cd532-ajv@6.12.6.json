{"name": "ajv", "version": "6.12.6", "requiresBuild": false, "files": {"scripts/info": {"checkedAt": 1752045736528, "integrity": "sha512-4EhvDHUq1P6v2DrRFulTGebQeVeujGLdAhvWeBdLh2g9nfQWa+arO7BqSE/mbbiXBEQXaHFdUuYB68jtfksrOQ==", "mode": 438, "size": 289}, "LICENSE": {"checkedAt": 1752045736529, "integrity": "sha512-pSvNrQ1v8mPbFOC9xj418VpthXfRCGIqlqtD9aM5dt8zZxZsJPaSM0hdVAAb2WLS02W7nwSlHqGH/rtmbFvI6g==", "mode": 438, "size": 1090}, "scripts/prepare-tests": {"checkedAt": 1752045736529, "integrity": "sha512-F0ODgoWsgSRy778Txg9GXKCLgIY3zzaB2Pe7f/0ALiJ4OesItoYm8itXpVNYJ7cF2YF/fRXrpEchU1fqsOEk9g==", "mode": 438, "size": 269}, "scripts/publish-built-version": {"checkedAt": 1752045736529, "integrity": "sha512-DaMjYHyZ6Vn0Xk5UbWwySbi04s3bW9v0gUSjJ7apBx3/UPolPd6skBm8r8yaIYGiAwydkBuc/5pjcGO4uCcjZA==", "mode": 438, "size": 842}, "scripts/travis-gh-pages": {"checkedAt": 1752045736529, "integrity": "sha512-DqRDq25cYN2TGJiD4b0+O8KqznnsINFOW49TuUCM4KvgZFr0BwTGfFjt0GUX0YGmkSxFRT+S4Xfx6R3KNaowBw==", "mode": 438, "size": 874}, "lib/dot/coerce.def": {"checkedAt": 1752045736529, "integrity": "sha512-j9Wy5x6GKxAMJ6jMxG/NaWqEZDWlA8oB62wJJLADLtiEYJYIZO8Ku+pPdOZR0cWNRdfwmm3ZG5AzsUUdffuTMQ==", "mode": 438, "size": 1979}, "lib/dot/defaults.def": {"checkedAt": 1752045736530, "integrity": "sha512-DsTfQCTG5Xh+eYQ8GAS2ATpR1RSOXXOq2PZq20CfmMxrwpQ2OrWHX90tszZiuj345TN86Za01/JXql2kPxB74Q==", "mode": 438, "size": 1285}, "lib/dot/definitions.def": {"checkedAt": 1752045736530, "integrity": "sha512-OsVxnBha3TWxZszm87oyVwMLuvPy8zCM7FhppaChWhd3Ixn5/eUpawYw7xd6Tuto6/ASn0qQRb5eXvD6z0NzoA==", "mode": 438, "size": 4022}, "lib/dot/errors.def": {"checkedAt": 1752045736530, "integrity": "sha512-j267ngVY6S1KwxzelehINgerY2NQmCRmibNrgkqaLTNyRyvTHUmO8bJDiOiVXbUwJylQBx6PjxVAqznkQ7Q5Sw==", "mode": 438, "size": 8282}, "lib/dot/missing.def": {"checkedAt": 1752045736571, "integrity": "sha512-B64CwCraCaap2DgBk6VePs0AyMrxb+oCm4iZVi8NsAIqOLbq0IG6rXYv6eRAZWsosOwhqGdOMQg2yqgv7EXDcQ==", "mode": 438, "size": 1194}, "lib/dotjs/_limit.js": {"checkedAt": 1752045736571, "integrity": "sha512-pMH3xUpUxQjkjR+7HO1D7dff1Fu6P2XtVhluZwZEVhce2WRjxfLO0Nkb08Q8v38UGWHDs7C633ltQuxjkd5Btg==", "mode": 438, "size": 7395}, "lib/dotjs/_limitItems.js": {"checkedAt": 1752045736571, "integrity": "sha512-tJRdjc0Bos8IcmLW9GfESJFTLf1NyYLjZ93f/ayqPpkOKuMy0ENH3fvdtDuAPopsgu/MrM2KfdvqzPEkADtP/g==", "mode": 438, "size": 2704}, "lib/dotjs/_limitLength.js": {"checkedAt": 1752045736572, "integrity": "sha512-uYBfrvOVtBPuGptE9G2QhbMmjvG6y8hD3rOpLgo5tor5B9x13nkspp+D+tk9XbebWEd/2ikqgGnHnMaqZAkKVg==", "mode": 438, "size": 2825}, "lib/dotjs/_limitProperties.js": {"checkedAt": 1752045736572, "integrity": "sha512-ohJBvsjbH7FxNOke8vpA5p8XLvYaNARHWzGFc9iIb/BDhAl/l+iLu+ACvyqf7Yb5bovDSJO897rBOZCWJVNgdA==", "mode": 438, "size": 2742}, ".tonic_example.js": {"checkedAt": 1752045736572, "integrity": "sha512-BbFtQN5Dnta3q/W7b37hF/5QnlV8Orw1gy+++rV61l+wkwnrQnMwgO4YzlV3DRAzDgVPg6V4VmJO1iqDA5fRHw==", "mode": 438, "size": 439}, "dist/ajv.bundle.js": {"checkedAt": 1752045736581, "integrity": "sha512-km2o1mynU1nR0HonrYrI0TA+QvRMtONwnfA/nl15hnd/WyjZ/FLV7NROHAbzzSjHxIeQPfiJRUSUzevO2Ut0Ng==", "mode": 438, "size": 272665}, "lib/ajv.js": {"checkedAt": 1752045736583, "integrity": "sha512-qP/QErWxCZ8Y8v3zl+5fHDjkKo0Ydi8hlylYG0Hj4j0iL3t79LBKxM2+2n02WAzwslOW9Yl5yPa+LZ0YYu/BYw==", "mode": 438, "size": 15837}, "dist/ajv.min.js": {"checkedAt": 1752045736593, "integrity": "sha512-+WCxUYg8L1mFBIyL05WJAJRP2UWCy+6mvpMHQbjPDdlDVcgS4XYyPhw5TVvzf9Dq8DTD/BedPW5745dqUeIP5g==", "mode": 438, "size": 121997}, "lib/dotjs/allOf.js": {"checkedAt": 1752045736594, "integrity": "sha512-0U9NAfHOH/A7Jssiy3AOjiYHloYzX1EcfhQXWqE/cUvNL3m3Tjy5BAdyZQpgo3M4JH5cWjmUTI7gDiyTyKNzRA==", "mode": 438, "size": 1349}, "lib/dotjs/anyOf.js": {"checkedAt": 1752045736594, "integrity": "sha512-fGGhxnCJM/U9r9AgSEOtfbEazmQsMfenJOQuj9PlM/je0XOMCSIIThbjE/+eJoFnw9oQApi9n4vBmS0dWcXiqQ==", "mode": 438, "size": 2925}, "lib/compile/async.js": {"checkedAt": 1752045736594, "integrity": "sha512-G30ILTAyBJGTf186nKWzMrntFb01mxoavqQLKaDfgoO3a4lPvOvdRPgzBpX46hqYbh82iQCOxlFoyZMBdr51Zg==", "mode": 438, "size": 2644}, "scripts/bundle.js": {"checkedAt": 1752045736594, "integrity": "sha512-AoVmISFOb40vWK5l5Obz+b7k09t3541VmeJbTKcoXgFd8qHY+nMpjjjKL/v10FjBCPXHLXgrc1Ux6mYbBLTYEA==", "mode": 438, "size": 1795}, "lib/cache.js": {"checkedAt": 1752045736594, "integrity": "sha512-8M5Udd6Mlq2w0cNhPfJkwWltL4M5qxvZIjVVNLLgbVMmQeYTFUlxd/ekJVk9EzgLRGiMcr12Nu4CrEmy8VYxPA==", "mode": 438, "size": 409}, "lib/dotjs/comment.js": {"checkedAt": 1752045736595, "integrity": "sha512-vX/wX9/g8JBngLetCVaVtJnLGdk/iZHY5VJgecD545d1LtbnM/Z7u4F8PyiLhE/8QCwZ6+0Xssp7/P5lvkgREA==", "mode": 438, "size": 573}, "scripts/compile-dots.js": {"checkedAt": 1752045736595, "integrity": "sha512-fG7owXhJ1V9bEMVpEl0BNBUwYszIhOWhfvZzAPZseeoUxgYNIOldKdkGSAxiwnnzxDr3VnEfLTuObOBoTvdPJw==", "mode": 438, "size": 2431}, "lib/dotjs/const.js": {"checkedAt": 1752045736595, "integrity": "sha512-aNkT6RhJ4CqDEmLLbJ5H5Byw1K+xm8zXQB0SL4asW9jA5r2lfVUdsPutvOSmY1GNTop7xQ/Nws5E0sKxaZVJ+A==", "mode": 438, "size": 2113}, "lib/dotjs/contains.js": {"checkedAt": 1752045736595, "integrity": "sha512-YXa7KJGMiFF3AQ5X/Y1Ox0R9aaJOcvZywSah+kspibfjBultLFJ4oBsOAcBNtQFK+ZG+bTEXk6phoDFj95AUww==", "mode": 438, "size": 3370}, "lib/dotjs/custom.js": {"checkedAt": 1752045736599, "integrity": "sha512-Kqf2FpGd6iH9BXzOsZYUlQ/GFEG6aTz1ObL6PHiJwua8vxMlZtgwuwNF/r30QWGYXNqkFeQCNjBrUY5tFG/knQ==", "mode": 438, "size": 9770}, "lib/data.js": {"checkedAt": 1752045736600, "integrity": "sha512-KB0UhJKbYCrkKAjqzPFkJ/pd1RlvZihKQAqF/2hk2RkwJT14bRrE2IlIOq4W8r9d6Wg9VgxseEGcChBS6MihqA==", "mode": 438, "size": 1048}, "lib/definition_schema.js": {"checkedAt": 1752045736600, "integrity": "sha512-35kwd4EIMo/TiuZ7jAzpsGO8fm9/ninHsjQQOEHIPmxBePzkbyh9bqpuJilk5jp2ta3tGpK7FZYTcYWxpIlRsw==", "mode": 438, "size": 872}, "lib/dotjs/dependencies.js": {"checkedAt": 1752045736600, "integrity": "sha512-5nEFDSpxZ+1vMw70jN6aUvUfcKzUoUjMKpTj6fj0V70CrbrFIpMnenUlHkrrd1hkPNQSLUpQPwUShDMIzot2OA==", "mode": 438, "size": 7720}, "lib/dotjs/enum.js": {"checkedAt": 1752045736600, "integrity": "sha512-smOAKJWZTeHqZHDheSWJKU9iuJElYilm8EbyXFLlprw1J90Wv+XsleG0DwiQMOpXz7tWdzF8c9T3KoJKBULeVw==", "mode": 438, "size": 2586}, "lib/compile/equal.js": {"checkedAt": 1752045736600, "integrity": "sha512-XGPaNJx6k/JAwxzCroBmSFsSTXIH/LrHxEGH6T3zltQjAvKdAih8gDgdCkzYsPfuZ0D2NW9/szjGLaLkObB+XQ==", "mode": 438, "size": 176}, "lib/compile/error_classes.js": {"checkedAt": 1752045736601, "integrity": "sha512-Q/yZn/8HaP01yBHy4T0HTlExnuXxYCk7gL+mPI+94tiutJpztcAiv82wf8VMklfz8vQeDDqPt5G63hVhKLmwwg==", "mode": 438, "size": 828}, "lib/dotjs/format.js": {"checkedAt": 1752045736601, "integrity": "sha512-bOFLjrkEhe2fzmfNdQ3a/GNIUYbV4c1YYDMQUw3JRWGZYxaX8PvVuNvW1FL/vOpp7hnzUWWlSwGc49Y95PEZjA==", "mode": 438, "size": 5565}, "lib/compile/formats.js": {"checkedAt": 1752045736601, "integrity": "sha512-jX0+JLHoj5NCtLekCV2hw9/f//n1ct4RxH7/PvueLSR6O5idpw0Ga5Mi/X1dm0FRU0kb2fIsRBKTOZgiwT2DfQ==", "mode": 438, "size": 12081}, "lib/dotjs/if.js": {"checkedAt": 1752045736601, "integrity": "sha512-J/VdhR2ixLx/3+0yOjo9N1Y/qevIp1l1qkNgocxR2t9yFAMei/nzccoLnD8aVmzclQVCid88P+AIZbX2gBuJVw==", "mode": 438, "size": 4159}, "lib/compile/index.js": {"checkedAt": 1752045736619, "integrity": "sha512-Gdd7pR6JhgkqffJcZFKjnlZ+ifu2OYbLZLsA7DttxtJUziZbROkO7AG4WA244iOV3zc69bhgF0HNeM2UPxGQxA==", "mode": 438, "size": 10695}, "lib/dotjs/index.js": {"checkedAt": 1752045736620, "integrity": "sha512-QJ+AQ55Ycif5mpOEJBTIF3HtOJZ8FbkwKqYUEnVdIs4RmzWbrs8PWzGUebWyw3whlVLPWlKCc6gEOQ24WrAvqw==", "mode": 438, "size": 1100}, "lib/dotjs/items.js": {"checkedAt": 1752045736620, "integrity": "sha512-7Z+iFWZ56osjDEkz3p179mvO7ohMEiKujQUjN4Q5U+lLrGO2RuOsz447YVIQ3CMJKa/eAmW1AFNqdm+U2ZluKg==", "mode": 438, "size": 6214}, "lib/keyword.js": {"checkedAt": 1752045736620, "integrity": "sha512-/PdokngUxJGpYatjvkDKY3lNZdYKqWEp3z2IqTvxknW0x5gTvkhSSUJ7aEkSTjQwHN1Lu3of64HFB+XDi8k2AQ==", "mode": 438, "size": 3911}, "lib/dotjs/multipleOf.js": {"checkedAt": 1752045736620, "integrity": "sha512-2rDAPltSfOXTDRH4GhwLNQOPHaAOrkx4lTN8JdMHWPeRhbHYasO3LdGxJAT8Y6SPkzx2J6GM8SBEZ9TZ7Yd1cQ==", "mode": 438, "size": 2807}, "lib/dotjs/not.js": {"checkedAt": 1752045736625, "integrity": "sha512-IKoUA3XmwB0cgPN64tOp2uA+oCXF6QacHTzcT8LxMuvjz9t2abuAtwotzy/02dytP8f7JvMkpemcoP+DIywlnA==", "mode": 438, "size": 3460}, "lib/dotjs/oneOf.js": {"checkedAt": 1752045736625, "integrity": "sha512-FPzmKkqMpR8niMcGPWTdqBpzYbi2U7nqBkSkZyJBHmFROfz3t3YPBc046HbW0dAh3WhG8GnitcnescLR7JSOIg==", "mode": 438, "size": 3188}, "lib/dotjs/pattern.js": {"checkedAt": 1752045736625, "integrity": "sha512-cgFmYp3/ypfOG5iKF71pvN2fjUzt0QTAYMUPMuQOGiEbarAw+lC6M8BVEuTAXrguP2+YXAOdn1cTknUTfk4NTg==", "mode": 438, "size": 2586}, "lib/dotjs/properties.js": {"checkedAt": 1752045736626, "integrity": "sha512-dxPde40ufob3ZjCaRp73adQ91hIYO9R78hukWOSzTdecMGc3Ort5OtI157Jlox5mJAOGm7AAer8SzngeMfLEeQ==", "mode": 438, "size": 15121}, "lib/dotjs/propertyNames.js": {"checkedAt": 1752045736626, "integrity": "sha512-2epXGSXUBn8qKvu6hEn19ZPq7yyIs7JGELi7za3w3HGOtWKn4BTEF9u/TG4vAKSEXFf4FicYZT2qGJ+fkJRtSQ==", "mode": 438, "size": 3607}, "lib/dotjs/ref.js": {"checkedAt": 1752045736626, "integrity": "sha512-nY8fOhoSfHo20KsHgDrCWzXDM8PWsn/KvAuUVqauxEv2sdKo7DE2ffvmCU+eNM70nqwjeGp/0UhsJkTNgTUqOg==", "mode": 438, "size": 4711}, "lib/dotjs/required.js": {"checkedAt": 1752045736626, "integrity": "sha512-4ZOnRfOz0x6ElRZ7/lmlTZsctNs9t44NXzYJ7vq/AcD79aZVp0FJ8/0f7R/zw0mvPjNkm04tJpjHVxxKc7ROgA==", "mode": 438, "size": 12395}, "lib/compile/resolve.js": {"checkedAt": 1752045736626, "integrity": "sha512-jWWPobKdCgGt1A5xwZDjquIGvKCAWgaJtn12Ad++zDc5jhxkIrZu3cXZSry8so8NXxbPySRraumtgIvgTzHKhw==", "mode": 438, "size": 7847}, "lib/compile/rules.js": {"checkedAt": 1752045736626, "integrity": "sha512-qadscVSxWnI9iUVxg8HPipr0BRWlqvmSkr2H3l/kaSUDhy1NoBVco0YNCCJ6mBghfqfWM62L8pWV/LV3++m1Aw==", "mode": 438, "size": 2021}, "lib/compile/schema_obj.js": {"checkedAt": 1752045736627, "integrity": "sha512-iFD2ym+Dun8eBMv3YqGkQgbusVmDbM5D/IZ2B+FOxRDztPr/8zTJ20TfajpyyMENuQ8s2C0iBuGBG1Nm+U7/qQ==", "mode": 438, "size": 133}, "lib/compile/ucs2length.js": {"checkedAt": 1752045736627, "integrity": "sha512-7fD/6k6aYyl20uCTHwGcMFUayyXFAmnGzClZYFafDjVgiAGj3WOteglQr8zQh4k+WJnBpXoumvefbfvwrfVwWQ==", "mode": 438, "size": 558}, "lib/dotjs/uniqueItems.js": {"checkedAt": 1752045736627, "integrity": "sha512-4q/x4bQlGEWIvJHB/4xShPOq8R5A5bOmv3a6bYzIJ2qB9FnL54O<PERSON>19ehr4CKsfyaEBdn+iO3F2M/iGxEFqf9XA==", "mode": 438, "size": 3694}, "lib/compile/util.js": {"checkedAt": 1752045736638, "integrity": "sha512-NBunOIlTThIKnxaThGLpFQwY/ak4ZFwUv/CmL1Aqd1taf1PGA1gVe0PT8JpyoNop5kK2tKkd44gOUCH3A1Xz7A==", "mode": 438, "size": 6974}, "lib/dotjs/validate.js": {"checkedAt": 1752045736639, "integrity": "sha512-5lAaZ+gzZCZ1C/+28PApCB2qTpH8SbM9rdRCRjoRmxEboGwDDcz8x86YHe2ykIJQjvqtmhn/Y96EzZ6aDlm58w==", "mode": 438, "size": 20018}, "lib/refs/data.json": {"checkedAt": 1752045736639, "integrity": "sha512-9yR+OoFz+KhBkbIXFgGeVv0VUZwdIKPLnkqPdTLfnyvGvNghl5iyzk6odg5jroKROEcOtmlUrT6eQUOguSHgEA==", "mode": 438, "size": 551}, "lib/refs/json-schema-draft-04.json": {"checkedAt": 1752045736639, "integrity": "sha512-qWZXa6unzhJy5gjNXzDD2KQN7DFKKaHJONCXW1OBmzOllCsV59Mw0spe/+m3xcEm91jhsxxvTTU0rnAbSSHhdg==", "mode": 438, "size": 4357}, "lib/refs/json-schema-draft-06.json": {"checkedAt": 1752045736639, "integrity": "sha512-l1r5n0P6wmsF+Qa5hy50McGfRYbZWyOB/8Lmo2wuqQBi75pS38Dz3CObVeM3lWuTd02kX5ScLjpOWRieu1/8Hw==", "mode": 438, "size": 4445}, "lib/refs/json-schema-draft-07.json": {"checkedAt": 1752045736640, "integrity": "sha512-RobO6OCa+0alsnc0B4gen2DV2SHg0p0SFQaissvcXI8sEJZ9fvgfwOnxxQgtsBsmBcrQ3uo8pclVU19P1KXIkA==", "mode": 438, "size": 4879}, "lib/refs/json-schema-secure.json": {"checkedAt": 1752045736640, "integrity": "sha512-unr5Tvg5UnVCkHs25MSaReSdOAxQ/Lc0eHMEc8FCyIpMqdcSum5PyaOcV3RVX6MS2SunnxbuRkddulZqkOMSXw==", "mode": 438, "size": 2572}, "package.json": {"checkedAt": 1752045736641, "integrity": "sha512-2QGM4VZ3HCrWN8A6DG/k3IuN+RHV8gFINph2sXtafUqnW7QX/gD4Eee20hpd6tiSD7hEckYeS426pXduWZ9h+A==", "mode": 438, "size": 3120}, "lib/dot/_limit.jst": {"checkedAt": 1752045736641, "integrity": "sha512-l7h9SbtKG22rozJcOtwsqrstA8bnttt4r5jtJ7s4rkR5F7UeOr9WOl3z9jaoMzeswyTUJavb9adwWo6FExi4Dg==", "mode": 438, "size": 3932}, "lib/dot/_limitItems.jst": {"checkedAt": 1752045736641, "integrity": "sha512-2qUrHsOWofWXjskB/W6VAqQo6foH6+vPfGVHmQ6cYK2c/Sl7mUkGSRtzyiBU8aPf9+j/WI0z2JQh7DI05k6yGQ==", "mode": 438, "size": 353}, "lib/dot/_limitLength.jst": {"checkedAt": 1752045736641, "integrity": "sha512-IYrwhr9oK0/skxokYLFzmFIMmvMb/By4yp6dGclDExPYYnVUTD0dKX1uVTVcItTTCjugMpr9Yu+yzQWiALPZEQ==", "mode": 438, "size": 358}, "lib/dot/_limitProperties.jst": {"checkedAt": 1752045736641, "integrity": "sha512-bE91VmZjmZpNDSPaMRHXrXAWNAXzVUDimKUkYldNYV009Oj8PGh+9/WHu6W1h8FTfF+qhFBFsjGUOha9jK7v6A==", "mode": 438, "size": 376}, "lib/dot/allOf.jst": {"checkedAt": 1752045736644, "integrity": "sha512-a+lrZJij89Q069obi2QmFgCGyhdRgk25BaPGvBPzPBWOSHGBrY4XTTd4ilrEkVNfnrFCHAHf0Ae5y/GXkoRNfg==", "mode": 438, "size": 609}, "lib/dot/anyOf.jst": {"checkedAt": 1752045736644, "integrity": "sha512-v+6+ZMAks4fJpL0w2VGB9ARAG8WnMAhwB5Pjx2uAPyT+mJZJ/3jlSv0v2547RtSWhS8VFEAWahfR2xp9dyYAlg==", "mode": 438, "size": 921}, "lib/dot/comment.jst": {"checkedAt": 1752045736700, "integrity": "sha512-TwOJlA99GD4Aofs1EfxNnBBBdZbFcm+jgyihXSbNUQXJ0+dW0Kq2YFHqzUMwazzQMRE/aLL0ftFCiS55f3qaIA==", "mode": 438, "size": 322}, "lib/dot/const.jst": {"checkedAt": 1752045736705, "integrity": "sha512-NQUM/ep34/HY2eZlfGoPEUajQMwSn3tZNH402eJPwhWJszX7YxAElElrI8ADlD12aZKAc6P8HehzaDlLkkBrPQ==", "mode": 438, "size": 280}, "lib/dot/contains.jst": {"checkedAt": 1752045736705, "integrity": "sha512-XaH0nEDai8ryQ0hsNZR3DHfEN8j/qxR846PK/Mek3Gzri2kHN/HUJGNQFT98/66Ilu8elIuvG0ko96zVjJ6KQQ==", "mode": 438, "size": 1188}, "lib/dot/custom.jst": {"checkedAt": 1752045736710, "integrity": "sha512-QH7dRKyoIAmHPd8lZBDfNd82fdsYD5uVbqEhoso6JF7jjsWGO/i0KuRpXkLSxvyP0sLW+Jl4tsjMWV83AdvhhA==", "mode": 438, "size": 4945}, "lib/dot/dependencies.jst": {"checkedAt": 1752045736710, "integrity": "sha512-Xa60N0dnO60ZOvkfnqXN3m/1N+itDCu5D+i8sXWnaCl9QLPHcAN3QjwjvUXFZ1qqocsfLtnIau3Pu+qoQM9BIA==", "mode": 438, "size": 1868}, "lib/dot/enum.jst": {"checkedAt": 1752045736713, "integrity": "sha512-VLDV9LsM6H4THVF8WnhGRXuvzWHTf9AmgcZbVMSMMVaN5nVgjfrphz4ItHJq9Xt2J3ANYtXDoWgPcRzqPtK2FA==", "mode": 438, "size": 552}, "lib/dot/format.jst": {"checkedAt": 1752045736713, "integrity": "sha512-1dFsNyU6Rz5Prge8k0gZRUXI6g1lLN6ZRqKDm5OHu1Qxq+pv9DizgcTfQsT9jVa5cQGHpnqVdcbxJC2pAHvNuw==", "mode": 438, "size": 3136}, "lib/dot/if.jst": {"checkedAt": 1752045736714, "integrity": "sha512-nJLxDVsr9+UelD4AfBAqHmYH4M1uizrhZIAW/mILOvIULZTxVpN0wwoxqcRpMc5rQa1JBCbz3rw+HwBkd+J3+A==", "mode": 438, "size": 1618}, "lib/dot/items.jst": {"checkedAt": 1752045736714, "integrity": "sha512-6MDj6SYDn667rNCkFMPceLWMNiZPuEz6vgyM37pNg9chXuO0JMfE1zZUYifTT3w/AEi3oYd7MNqq658gflCbiA==", "mode": 438, "size": 2610}, "lib/dot/multipleOf.jst": {"checkedAt": 1752045736714, "integrity": "sha512-DdXe3Hku4O9wwg1/IaSYAIAmgj6jclHZcKhjIGnfVNZIMUkJVJnn0pWBKwiz5pSHKJvAfcmTk+Ct6WgM2sTWjg==", "mode": 438, "size": 644}, "lib/dot/not.jst": {"checkedAt": 1752045736714, "integrity": "sha512-cUhpMAuJZm3dUIog3fhlYY6h3RxAApaJuIcQvWgWKNPMFBDXbtavpt3JgbRtWOhcsCUB5bPXkNoRE6yeotLpFg==", "mode": 438, "size": 861}, "lib/dot/oneOf.jst": {"checkedAt": 1752045736714, "integrity": "sha512-+ktFdlP4Qngl1Af/Wk23eTBvVFEhyCA1Mw+67KW7N6x0UaATlvC+x5Ela15+2YoVdOTi7pITgMDh6B4t9qe7Bw==", "mode": 438, "size": 1150}, "lib/dot/pattern.jst": {"checkedAt": 1752045736715, "integrity": "sha512-SGsGQoVeGGmSBxpYi9gnp3FN8KO7Ri2uPRaadDS7HnEXXp9r/psgFVtrLu5V4smQl9vOysLmrjXJy/dCV3t6cA==", "mode": 438, "size": 348}, "lib/dot/properties.jst": {"checkedAt": 1752045736715, "integrity": "sha512-evgbMHtH6QHN2pqPgHiZMkhkSgx/y0nThAyHVzA8g8yLfvSVjc1afrL0orSDJ6l84Xv8YnfzbSvVu1VYDbNoQQ==", "mode": 438, "size": 7725}, "lib/dot/propertyNames.jst": {"checkedAt": 1752045736715, "integrity": "sha512-2P3i9RGQ6JKF0Z6tV8B/1qnVsVEWEXAg6Y7NElL9lo0qktnSWWlCRn2Eti2VIpUVWEFdYSzGMssemZ3Zol0E0w==", "mode": 438, "size": 1284}, "lib/dot/ref.jst": {"checkedAt": 1752045736721, "integrity": "sha512-yG+/n2xmo1ucXOapjVyeMoZFvp6VvXJ9+CqsidtK7piyXW4JzmWjLzaQW1OHg7lDeoROy6o3sKbnCTvQWDGKYg==", "mode": 438, "size": 2454}, "lib/dot/required.jst": {"checkedAt": 1752045736722, "integrity": "sha512-wW4sskxJAolkkzMpkjXIC7AGNdO+Zat5wPFNZTW+PpaAGSwfzrgOm4ho3gRN95dZ0j8iAOWubYJSaUIuQJO9bw==", "mode": 438, "size": 2868}, "lib/dot/uniqueItems.jst": {"checkedAt": 1752045736722, "integrity": "sha512-fqEXKeFDNhJDTko5asfVkeLOQOD27n5pXoPy66c4FSmjgrBVjInIC8loXwu+JlZUf48DS40jXE+wZVUSdLzSoQ==", "mode": 438, "size": 1696}, "lib/dot/validate.jst": {"checkedAt": 1752045736722, "integrity": "sha512-G0uQ9to5SRw/2sHjP4dfekQfjuwzj6u+AcVHVVFYw4Yy5smRhbt4JHI5S6EAbVFLvtG6aq527BazHxY2uGRHJQ==", "mode": 438, "size": 7717}, "dist/ajv.min.js.map": {"checkedAt": 1752045736724, "integrity": "sha512-a0NE7VBmbh4jxV4nxD3O1aJ4RPDRKwBmqZjqvXwrJC65I1ugmGZd01p5ZV9Lmi5+H4jZQ/vxQsJHR6WTQE+2yw==", "mode": 438, "size": 140201}, "lib/dotjs/README.md": {"checkedAt": 1752045736724, "integrity": "sha512-HVgzlqbCQPMBwbT7G8NRQ5gW2cZDZWgucwSeKWhV/JoljN5zveNLj8kSkVFIfLbdngQpMzTagpmgGe1LlPgDQQ==", "mode": 438, "size": 149}, "README.md": {"checkedAt": 1752045736725, "integrity": "sha512-2rGnhDI7LwLv6kMteUeiR81IymmhATYjP+kfazIUkHc1UKGDkJvae6ibJIvBopDUpUWUDtIICWtNlhsYxQG1gQ==", "mode": 438, "size": 85598}, "lib/ajv.d.ts": {"checkedAt": 1752045736725, "integrity": "sha512-gj2qZjwNZhnkIL+6D5QhGEsfB0J/7+i38m2xEfCT+lVEGkSS2iIrZOfL4uM7Sx6UzPyyMGGBI4ivpVH0o34qwA==", "mode": 438, "size": 13221}, "scripts/.eslintrc.yml": {"checkedAt": 1752045736725, "integrity": "sha512-PeOcZ6FTPrkuJ47RThZpCi0+NTrstJphluJIvZ2gwid0yfAIgwyq/6mQMfd0/7KmvQmvoL6YfxT62Vk3K5VJWA==", "mode": 438, "size": 62}}}