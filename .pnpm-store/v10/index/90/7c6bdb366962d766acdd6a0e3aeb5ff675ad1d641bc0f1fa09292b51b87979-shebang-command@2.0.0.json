{"name": "shebang-command", "version": "2.0.0", "requiresBuild": false, "files": {"license": {"checkedAt": 1752045715059, "integrity": "sha512-QQmk4M/jfBcyygmcqkvREGxOKYqfHdUIKM74BnQ1zGaNq0S+fUpNo/uv3aWu7iKuXEJBbPedCZYIl4PLE7L/Sg==", "mode": 420, "size": 1116}, "index.js": {"checkedAt": 1752045715060, "integrity": "sha512-ke6l3VBCfYsEfgo3oeF+HPhVuX1jolfdY0cUWTYQDH4T59KATrzxC8D0BwkKRELj1r2Y981Gpjdiob9i8rvIqQ==", "mode": 420, "size": 387}, "package.json": {"checkedAt": 1752045715060, "integrity": "sha512-RSLnhnXEL+fcLDY6pXhB2zk6R6rZ/JS6XW3O+pMtosR37IvqXq95BRGxxDj5SkA90AQbW50AXZlR+Qbv/VO5rg==", "mode": 420, "size": 558}, "readme.md": {"checkedAt": 1752045715060, "integrity": "sha512-z4JzoMGGqrM6pKE/AfhgYNOQ/U3whlefZxt4OpGtuV5y28WqkZh6AnR/0A40a9z1CJpwl2vHL1kE/CfWxVWbTg==", "mode": 420, "size": 495}}}