{"name": "@sentry-internal/replay", "version": "8.31.0", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1752045724231, "integrity": "sha512-W7Vf3q2KENc0DKucOzfbzN9DYWT5kjfwKg54IW9ZTYLTpWtlbkX0SZs/mOkmTItD1pPMD/9dbGSmR2HdRRvj4w==", "mode": 420, "size": 1098}, "build/npm/cjs/index.js": {"checkedAt": 1752045724245, "integrity": "sha512-+ueCV4iik3Z7IZdCtIGx2iHuRk8U1ue6T59Vw/aw5ktTayo1sf9F7prC0QtFJTL7J4piaEKsJ9nEXZ2v/FBu9Q==", "mode": 420, "size": 314036}, "build/npm/esm/index.js": {"checkedAt": 1752045724251, "integrity": "sha512-eCHudQp+cRqu3vcBJpfMsHYxCM08YFebOtvEP8L/atRvApOYSsXJp01Ogcm6AUNp5dKkcT5eZm1EGhhjXXQ6og==", "mode": 420, "size": 314324}, "build/npm/esm/package.json": {"checkedAt": 1752045724251, "integrity": "sha512-8U9TOhz9gHs1PCaXJpm9sUJG4HoouaT8XphE0/fVh+JpXFYtsXA5E4bfXXySJP6BBPyD48CV+O1IIMGusCIWoA==", "mode": 420, "size": 37}, "package.json": {"checkedAt": 1752045724251, "integrity": "sha512-xSF6NU3SJdmBY8rbRkkUNmgt6hMMUcZ3sf99UJfY7z+DHHzMWGmynH1wEj/GfiOFnej7ZrSNq4fv8KIYS8FdNg==", "mode": 420, "size": 2878}, "build/npm/types/coreHandlers/util/addBreadcrumbEvent.d.ts.map": {"checkedAt": 1752045724252, "integrity": "sha512-1gmQ+mtc1L2ujqu26kpVjMoBNtr7eg8KGlW+EACd8qU5ZOjmf/sneG3cte8AZ8LncLSzOOVoXUWE7IzLEJiE/g==", "mode": 420, "size": 338}, "build/npm/types/util/addEvent.d.ts.map": {"checkedAt": 1752045724252, "integrity": "sha512-nWAmaLYVCxH1Vw9b1aVgkQPSXZpsxPcRK2vXf7CJaPPvenHEJ3ZpSeZFYITkrGkPM09JeOoSMif5QfOthCMF/A==", "mode": 420, "size": 533}, "build/npm/types/coreHandlers/util/addFeedbackBreadcrumb.d.ts.map": {"checkedAt": 1752045724252, "integrity": "sha512-9NcSBuB3+0eCM8mffYBWBqdNhG8tSEW97yDPonbLdez8urSIQvnqzELoJopaTPbxaxbOBN5ijFr7Bngb8yS08g==", "mode": 420, "size": 345}, "build/npm/types/util/addGlobalListeners.d.ts.map": {"checkedAt": 1752045724252, "integrity": "sha512-YZdaaOfEp64dxD2EahKt2U6LfM45PBFnuUHXdSDvnKWBxX12Es68/63atQWjHo0rVD/9//TVdozGNexzLxoTrQ==", "mode": 420, "size": 256}, "build/npm/types/util/addMemoryEntry.d.ts.map": {"checkedAt": 1752045724252, "integrity": "sha512-Vn1/QpRTsR3I6K2i3l8qj9OX0q3AQ5cQJYQjdKeSTsOhSkY45HK/RmuxfxWqb8JxvMfhONi+cjZe/tKAWSEQBA==", "mode": 420, "size": 297}, "build/npm/types/coreHandlers/util/addNetworkBreadcrumb.d.ts.map": {"checkedAt": 1752045724252, "integrity": "sha512-Neq/wvNh/dLbkiKRQdJIClXJ7+RDwLdLiwRVapRQNaznU5Mm0TJbEfwbxzkHQ5qJFBb20Fs4fZ+4yrDJb1IS4w==", "mode": 420, "size": 353}, "build/npm/types/session/clearSession.d.ts.map": {"checkedAt": 1752045724253, "integrity": "sha512-EWAAHvjVx/jgJiiXFmSERKHurg+uBw6PaUgxdC6IIbSzCsxsLbCrKLyua8UJL/iJXqxvzyCKlCX2s8mpETKtew==", "mode": 420, "size": 246}, "build/npm/types/constants.d.ts.map": {"checkedAt": 1752045724253, "integrity": "sha512-bWFzrhs3ja+pkMOXT1Id8nTEdsOLTMkzhxQaKx67GJmwPurHv8iJErnU8gIP1Rg0ZcfESUbAH/WqcZLwZmS3ng==", "mode": 420, "size": 881}, "build/npm/types/util/createBreadcrumb.d.ts.map": {"checkedAt": 1752045724253, "integrity": "sha512-0E72kOk6+obE8ai6MbYdmz9Pq6tRhEJoG9NezXsvdiNNih121wjT3vpJQGleNaIi17DTBSewI7+2IkX5U5dBKA==", "mode": 420, "size": 348}, "build/npm/types/util/createPerformanceEntries.d.ts.map": {"checkedAt": 1752045724253, "integrity": "sha512-2MXkSekaPr/gf2X5COZ+d+RUEejuavao+0Op1zrSpmYvDu1Kie5AbG+BXl7AMY+c5dOw7hsDHTzTaG0SCrMGQg==", "mode": 420, "size": 1269}, "build/npm/types/util/createPerformanceSpans.d.ts.map": {"checkedAt": 1752045724261, "integrity": "sha512-sDGinn4vJYQHBJ0THHCvdF+ni1/HzvfHZud95kf5ZtTtGXPyA8IurWOvbjhItI4GGcXJpxkWjgvSA0oiIaxW+A==", "mode": 420, "size": 371}, "build/npm/types/util/createReplayEnvelope.d.ts.map": {"checkedAt": 1752045724261, "integrity": "sha512-4+hVE9OrA17c+LkNJf0HkMmCyKXX2w4CVU3HPx5vXyptb3/rRSUm/bNi7Qw7Rlj7lWXJ0jd2YH/j9mCh17pcww==", "mode": 420, "size": 364}, "build/npm/types/session/createSession.d.ts.map": {"checkedAt": 1752045724262, "integrity": "sha512-vpyJrh6Sn681LViLK1suwo/RMNcNSNt9lRnS5d4QKIheMltZVwgVSx3P2MZ7zUCYuYz0CES9USvHdNdk1VhX6w==", "mode": 420, "size": 455}, "build/npm/types/util/debounce.d.ts.map": {"checkedAt": 1752045724262, "integrity": "sha512-C3m7aDjOdPYHrcmTxJkfl32VsYEhXtpV+kDWft4jgTcke3L953rzS2dQMVZZPDt+twt5kyQtDw5IodbgYH+mtA==", "mode": 420, "size": 488}, "build/npm/types/debug-build.d.ts.map": {"checkedAt": 1752045724262, "integrity": "sha512-j93MM2yE31Lf4/veFCBcgxp2sddxINjf/31X/7PmlLgzWdkBX+RnKed0vdDVNmKPIxEm39StS55ik7IcMYYJyQ==", "mode": 420, "size": 165}, "build/npm/types/coreHandlers/util/domUtils.d.ts.map": {"checkedAt": 1752045724262, "integrity": "sha512-wCRDHXSkwvO5KFcZ2jRCQCxocGEhFtzocbBxIJzICi7JOhW+j7qRfxxgAX6RdJRti31jtKCp2wbE+BB51Ep6SQ==", "mode": 420, "size": 495}, "build/npm/types/eventBuffer/error.d.ts.map": {"checkedAt": 1752045724262, "integrity": "sha512-RrUVokPRszBFoh/wp2TdXwugSFnzBWdUg0m5ToqfgmveYEHFZxy2VDmR/yvDafro2XjG0lOSqWZuiVM0cuApLA==", "mode": 420, "size": 172}, "build/npm/types/eventBuffer/EventBufferArray.d.ts.map": {"checkedAt": 1752045724263, "integrity": "sha512-ry6se90y7tHkfEgcI89iFgkHvHOkMLuXvhC/lES/CqIZuVtfvlun/ToNiKnMFKMlmIUWWV5p9kL1reuaNzeLfg==", "mode": 420, "size": 692}, "build/npm/types/eventBuffer/EventBufferCompressionWorker.d.ts.map": {"checkedAt": 1752045724263, "integrity": "sha512-kA+luyKSCgyfnbGISZT+7RvuUhoBjB9N7JnVa+J5wdxn13tGaGWd2/nqPxPcdEhgOdMy9fww5TyLIYDYIZMw7g==", "mode": 420, "size": 902}, "build/npm/types/eventBuffer/EventBufferProxy.d.ts.map": {"checkedAt": 1752045724263, "integrity": "sha512-cJfiptcuNUGoEtfr2hpKrSXNgDQAt2PvSHqe2EgWKuXJLkw0kKV1pOKisgn0WjSzvmD2Xu4s3ffcFSpMuJTJLw==", "mode": 420, "size": 968}, "build/npm/types/util/eventUtils.d.ts.map": {"checkedAt": 1752045724263, "integrity": "sha512-kK/7MVLOCa1bx287FoKloUEyXkVaFrHaZ64iul+iIqOkicjCVajsXTI/BjXfWYPJ6bVZO1tk8/MGesADTfdK6w==", "mode": 420, "size": 523}, "build/npm/types/session/fetchSession.d.ts.map": {"checkedAt": 1752045724263, "integrity": "sha512-pk8SC5vcmEISq8H9hpIEb2TGtwD8LHZqKTHybxmWfpv85uVuHSxqVZffS4Pp1c2deLEfriuGbVB7l/+akBhGTw==", "mode": 420, "size": 235}, "build/npm/types/coreHandlers/util/fetchUtils.d.ts.map": {"checkedAt": 1752045724265, "integrity": "sha512-PXeOP5h+Rv7lLGZLYpkWxtI94mHV+CdllAIpRdJRGTlvLs2PXN1fEUlS0aob1WOEKNjxitRUrv69kZ8gUsn2Hg==", "mode": 420, "size": 886}, "build/npm/types/coreHandlers/util/getAttributesToRecord.d.ts.map": {"checkedAt": 1752045724278, "integrity": "sha512-vfUAxEOwMYAVTLXqBjv9vnEzYiMLmx+D345kWEogWglgl8vqsdcSKLKIymcb3b0D0yr+k9O5QSHx7rH0zy7pdA==", "mode": 420, "size": 283}, "build/npm/types/util/getPrivacyOptions.d.ts.map": {"checkedAt": 1752045724278, "integrity": "sha512-okF99q1Qrfnk9SZsgez8HLzoh18ERQfX0GwmhUmtG9qEuT01ybjC1wyscD/f9g5SkhIjIwwXspl0neF6piAj0w==", "mode": 420, "size": 604}, "build/npm/types/util/getReplay.d.ts.map": {"checkedAt": 1752045724278, "integrity": "sha512-+gu40vByRgImkjtqyu1/KFXRs1VnbgADkLV0nqbz0oMRGRwrVE0ARqeS0lli6K4evFHWCkyb7tnzz0IyVC6cfQ==", "mode": 420, "size": 251}, "build/npm/types/coreHandlers/handleAfterSendEvent.d.ts.map": {"checkedAt": 1752045724278, "integrity": "sha512-cCAG09ONuY2wv4Ucht22M6/4l7C/bx8CJqkH86x6O1sGnu0m2SoySLfbDPUdDhvXvlAOySMrFTC1VzHM265tbQ==", "mode": 420, "size": 409}, "build/npm/types/coreHandlers/handleBeforeSendEvent.d.ts.map": {"checkedAt": 1752045724279, "integrity": "sha512-QQb7rX/qfs2hpe3SsjhD/wauyKvfN7Dcd1UeQLEvZF+imG+3TjovSzKIMjl+q8xQjQrs6/uM2lBckn2La9Osng==", "mode": 420, "size": 375}, "build/npm/types/coreHandlers/handleBreadcrumbs.d.ts.map": {"checkedAt": 1752045724279, "integrity": "sha512-te6pKKhYo3K1RG2v07lq+ox3J0l4EKYy4j9/lCOHLYHFPxmhpaGoJQvRtcw324zCG/tvq82OMG+Ypg9oNmNtBA==", "mode": 420, "size": 614}, "build/npm/types/coreHandlers/handleClick.d.ts.map": {"checkedAt": 1752045724279, "integrity": "sha512-C/7qX+gfKQmDIQflVNfnDPfredymq6sTHuOjNPRpT/wzlRoHNfIOKlVRVczajclaAO0ZEzp24EYDkbMLvP+kkA==", "mode": 420, "size": 1495}, "build/npm/types/coreHandlers/handleDom.d.ts.map": {"checkedAt": 1752045724280, "integrity": "sha512-kz8OZIJSme+5tSGc2++u+W5K6qjcww5MYxXw/1LFhRYMwgDnR3f1dxYHeArEA8VPHcMRu4fpwFP4aRFgQM/f8A==", "mode": 420, "size": 498}, "build/npm/types/coreHandlers/handleGlobalEvent.d.ts.map": {"checkedAt": 1752045724280, "integrity": "sha512-0khlCWINUmD4BpZWCfcrGz++rxzueRchakA8llVRlglKO7qRLMoZ2QgFRHDKfUb4Rh+CxJ8OgfTzmlbxdRDtrQ==", "mode": 420, "size": 373}, "build/npm/types/coreHandlers/handleHistory.d.ts.map": {"checkedAt": 1752045724280, "integrity": "sha512-iBk/PVRaoOwqkFFHsoZcJOdopNLBp/1FpxOEpwe1dqyLL/QFJJlVuD+2uh0N5uiEIo7htnGMBRHo1r6oHTPSjQ==", "mode": 420, "size": 331}, "build/npm/types/coreHandlers/handleKeyboardEvent.d.ts.map": {"checkedAt": 1752045724280, "integrity": "sha512-uz6eQcEX1j9suVGj6j7uk5P69qnQ43oVIXNSQpLmcFkQ6WkTvtOO8TECLUnHhso4G36VFnzyIGb86cXceThw/g==", "mode": 420, "size": 413}, "build/npm/types/coreHandlers/handleNetworkBreadcrumbs.d.ts.map": {"checkedAt": 1752045724280, "integrity": "sha512-7+rRRDwjiJkcTPNapoJ0dI7ykTnblDy3WLtbMjP4uhdcywTBkwKGIfopm8xCF2C2UI455rl01T8dYaQdscVSWA==", "mode": 420, "size": 528}, "build/npm/types/util/handleRecordingEmit.d.ts.map": {"checkedAt": 1752045724281, "integrity": "sha512-zZGilrrrjYyxAvIDbYm7sAyvMDLNuoTkhgDDIlwwtXzGwLSx4CoKdSC0wZzc+ZkO1gqjgxUBm5ATR5bNSUJm8g==", "mode": 420, "size": 436}, "build/npm/types/util/hasSessionStorage.d.ts.map": {"checkedAt": 1752045724281, "integrity": "sha512-LfGUTwvSVra5eQPFcqJX66G3Q9goz1xvd8X+7AUf/pW2ibpP2cyX04saKD1BgW5YS3ERoV76eOfGVaV2zzLilA==", "mode": 420, "size": 189}, "build/npm/types/eventBuffer/index.d.ts.map": {"checkedAt": 1752045724297, "integrity": "sha512-z1Hqx4NV80/qu4P9j9TOpY0u/0vfw978DDZQa4x1WStHzbpm2Fh9trTVGIlej5IwPm1qMgYA55IC9g7p9rDhHA==", "mode": 420, "size": 350}, "build/npm/types/index.d.ts.map": {"checkedAt": 1752045724297, "integrity": "sha512-zBeU1aXpVf/ioTSt8k0tmLMTQG/iEvcaIjzmPhLdVAI5rm1wGgu7SpqE3okFN6v98YGargEDAH+JZgn0pshEHA==", "mode": 420, "size": 372}, "build/npm/types/session/index.d.ts.map": {"checkedAt": 1752045724297, "integrity": "sha512-JyJh13bFlIofQIlSp3LxpKVBUGRWqd2dt0nvb72sAWBgUNV+ma9VfrFGvN5oSpSDUcHd85YKK6TRLMlhsvTtjQ==", "mode": 420, "size": 142}, "build/npm/types/types/index.d.ts.map": {"checkedAt": 1752045724298, "integrity": "sha512-K/QWjBKEXAHe3XREClNaQ2u9yDek7WZz0kq2KAS8+wTgAZga/c+Jk85a77kC02OEhNyH7LJJV98HXsb8fxuLGQ==", "mode": 420, "size": 222}, "build/npm/cjs/index.js.map": {"checkedAt": 1752045724357, "integrity": "sha512-4pM9VVSKRQh4UWjvmyqqmTyXwf2TuskRHFcOykbDRZZUEhMK1z82RlcRj6Ru2BbJkcaoWAScY6FZhx01nX3Kpw==", "mode": 420, "size": 1450512}, "build/npm/esm/index.js.map": {"checkedAt": 1752045724406, "integrity": "sha512-42nPbYKDS5cvKC2wUlTxIVDdD/Ctx8bC4R8HL7+shRGwIjwg6kBShxC2Zk/Vaw4n/xnr4y/RQ5i6qMWh9tX8Bw==", "mode": 420, "size": 1446866}, "build/npm/types/integration.d.ts.map": {"checkedAt": 1752045724407, "integrity": "sha512-WBOi5SHCm/LD+JtFr56AXFqJz5svEEjcJ4+8uDlB2G7ugEF+7/1BYkmQHS4Tl0VGZTe49+JG/wdYWxfSxnZplQ==", "mode": 420, "size": 1469}, "build/npm/types/util/isExpired.d.ts.map": {"checkedAt": 1752045724407, "integrity": "sha512-h61QVkQy36K47Kgodg0wQMQAU3uixLZiyt0Mh0TSFjWqF0WC5kNiCiGv/9ycE9xsVLuQnmiiKmVVKb9BFdYocA==", "mode": 420, "size": 254}, "build/npm/types/util/isRrwebError.d.ts.map": {"checkedAt": 1752045724407, "integrity": "sha512-oJNIItiby46mytVN2npaBCa0Y2WnryPx2P/Gl2wElD0ISnfvZW1zMIuQ3sMsd6/RwWAOdoSBfgKTOVPQUdpbOg==", "mode": 420, "size": 271}, "build/npm/types/util/isSampled.d.ts.map": {"checkedAt": 1752045724408, "integrity": "sha512-zJz1c8+ZDpoDKHbdaPJZUZ2/3ib8JEBzFVtxH46fma8uMaRafmqenekGpvzwH4CEnYe6uT0LJVF74imKwtDhzw==", "mode": 420, "size": 197}, "build/npm/types/util/isSessionExpired.d.ts.map": {"checkedAt": 1752045724408, "integrity": "sha512-Y7fR6FI0SOCOIRhN+fpxn2NZnqCYyRnbTpFHA1LR4XJs5YWjqBjhOdOzB/8uRo7o7SE3xzvdC6zbSdROZ34nlA==", "mode": 420, "size": 395}, "build/npm/types/session/loadOrCreateSession.d.ts.map": {"checkedAt": 1752045724424, "integrity": "sha512-XaCB39CfugukCWvJog9Ptaf+MbNKxJjF8Glh4uLoJvpIYfj1jTuKH+yu0/0eQ01yLUyNKh8bkPlPIzDbWvpzcQ==", "mode": 420, "size": 420}, "build/npm/types/util/logger.d.ts.map": {"checkedAt": 1752045724424, "integrity": "sha512-bb<PERSON>yiIMvwTSu4MT0XXglN6qkOulT0B20z6QN7PQ7Ao+2DACmNKxOPi4Fcft7mrSUg7Uecr476/ZXrJVVlMPIQ==", "mode": 420, "size": 652}, "build/npm/types/util/maskAttribute.d.ts.map": {"checkedAt": 1752045724425, "integrity": "sha512-bzA8EXmx9PCGZ34wnCYccSPm672TJKu1/BDk8bZhfxqFVk852H/lcFhRQeug9y5D7RI+JFyi70T/jzS8sv7UQw==", "mode": 420, "size": 515}, "build/npm/types/coreHandlers/util/networkUtils.d.ts.map": {"checkedAt": 1752045724425, "integrity": "sha512-DTs+8oeI+bSSSY8VgJxMxPoIqOrlO9rcIjpiEuq8IbTfTgmsjF2puPbiSHAHYmVPO4XxLGGFYQ0OV0Z+0mWaBQ==", "mode": 420, "size": 1368}, "build/npm/types/coreHandlers/util/onWindowOpen.d.ts.map": {"checkedAt": 1752045724429, "integrity": "sha512-cHXnk6w0+qItgco9LFFLXcJfKjkHMsbQOU9bPky6E6T3qGvTSTHYpcGfRLfTfSg/e6ewkr5PRPJc+O4bN4UpJA==", "mode": 420, "size": 257}, "build/npm/types/types/performance.d.ts.map": {"checkedAt": 1752045724430, "integrity": "sha512-tTBeJWATViWNAL/jq7nTUrs+mFecW/PMInzE+68W6B97i/9ab6/UNBnbyIWALGPw36OJsUG0if10REEf8I1dwQ==", "mode": 420, "size": 2284}, "build/npm/types/coreHandlers/performanceObserver.d.ts.map": {"checkedAt": 1752045724430, "integrity": "sha512-1e0ZHkKF82O89Fbj8Z1WpJZV3D6OrOrQMyGq2RbziF+r1PSsgx4/epUqPz/aA7KIsF+5oE3qLmdxoxUDxCb5TQ==", "mode": 420, "size": 272}, "build/npm/types/util/prepareRecordingData.d.ts.map": {"checkedAt": 1752045724430, "integrity": "sha512-G<PERSON>ze9UHoWK+VyO9+qqUbexceIQ0cozRNViJ68h4/e9WM/56y/OjTn3xIHwFmgo+aDIk1mL9Poo8UG1Xea+Kzg==", "mode": 420, "size": 364}, "build/npm/types/util/prepareReplayEvent.d.ts.map": {"checkedAt": 1752045724436, "integrity": "sha512-yIbPPw66A4s4jtoUOJR9j3qbOTgiJFGOVanda+xUsICmAOyNd+kXkrJYZ6mb//yrTH7ACCYlLmHaXALKEaHVKQ==", "mode": 420, "size": 546}, "build/npm/types/replay.d.ts.map": {"checkedAt": 1752045724436, "integrity": "sha512-d3nXLj3PENXkI5Eigv/SROmanZYco/G9fwNYL228H/IdEpjW+ESLKWrULMhXFGDXKQMnWTNEtB0D3Z4DHkgy8w==", "mode": 420, "size": 3664}, "build/npm/types/types/replay.d.ts.map": {"checkedAt": 1752045724436, "integrity": "sha512-yXswpHt41HDnaZKXhjqZqx0Oo3dBsdhQg+B9vBX7dny6hEeqi/H9yxav95alpKL+OW2sQ9sXuet3QeOCXqauPw==", "mode": 420, "size": 7447}, "build/npm/types/types/replayFrame.d.ts.map": {"checkedAt": 1752045724436, "integrity": "sha512-eCs38lwU2dMOejRiSSROLHjX1MreBnKwsnVRMWPtXDneS658/Hkipvi+PIsatobUYGF5Ty23kD0S6oeOAXvAWw==", "mode": 420, "size": 4787}, "build/npm/types/types/request.d.ts.map": {"checkedAt": 1752045724458, "integrity": "sha512-7J<PERSON>OoWAHUuTcwEkMYjIrDUNXlItiYEfO2ZEtTtZZCPzwwO8yEWZHp/f9E5ccUP4vSA4oR27uHNmazdTIDLl1ng==", "mode": 420, "size": 590}, "build/npm/types/types/rrweb.d.ts.map": {"checkedAt": 1752045724459, "integrity": "sha512-KS8XC4AAc9b2rPzxt50siZlFmm72rPAOsqqNyqK8jFwf/+hQWe4Dgc5i5AnK7xe2aOd/8UgW3K/Lq5hodz2TyA==", "mode": 420, "size": 1837}, "build/npm/types/session/saveSession.d.ts.map": {"checkedAt": 1752045724463, "integrity": "sha512-BQbPQkVLQmkY0zpOmRNzSWZdRLiggDqvNtkBOVJYS20cF1bggCxioMVzXeXOBzeFwywgJUrQvo1wG9Mdbjji2w==", "mode": 420, "size": 242}, "build/npm/types/util/sendReplay.d.ts.map": {"checkedAt": 1752045724464, "integrity": "sha512-nHUWsqvVbEUc3oKUH5/BRjpHDR9qBFDtIy+RutPfwX2Lk37sMiswESLA7saJRNngGJkUFWnHtUVKIlS87Y82sw==", "mode": 420, "size": 272}, "build/npm/types/util/sendReplayRequest.d.ts.map": {"checkedAt": 1752045724464, "integrity": "sha512-hcqORoOtmlUiSiTp5EBeDhoJchbCQqPVjZPfQOgSx6Sx2B3TiszcHNSDr/UgAY8C1DI1w85LMu9VH2QBoYpdoA==", "mode": 420, "size": 594}, "build/npm/types/session/Session.d.ts.map": {"checkedAt": 1752045724466, "integrity": "sha512-CEzbq+ryzGkMdT+AWOOOJeZfWFKO54nqHySZ4nF3HpmgvhKevylxucoVulgolVUO41wAlU8PYdpca78MSAN6Ug==", "mode": 420, "size": 295}, "build/npm/types/util/shouldFilterRequest.d.ts.map": {"checkedAt": 1752045724466, "integrity": "sha512-Joo/6ObEtzHg8XDofaIG4mLHrLpv86MxJoqp/INCUzfr6ToDeDA1Zq25x6m4JrpgTYq0+Uivv5+KA5fpB2zk0g==", "mode": 420, "size": 278}, "build/npm/types/session/shouldRefreshSession.d.ts.map": {"checkedAt": 1752045724467, "integrity": "sha512-qstymYIAtLjFMNeTdCkbQ9zoasJ0XLLqCt3+oTP7aI5wG4lgeW3cyptPd8iaGWG6z1+jWQVI9ihL+9ys0o89CA==", "mode": 420, "size": 364}, "build/npm/types/coreHandlers/util/shouldSampleForBufferEvent.d.ts.map": {"checkedAt": 1752045724467, "integrity": "sha512-J/03Mnz7v0HHvqgYz8riIuhelHo8Hd7ldqqLZf9sHa7ez40iL4Ttczd5uyMNE+8OwwWL7z1x7eHOnvsWzmEZzg==", "mode": 420, "size": 356}, "build/npm/types/util/throttle.d.ts.map": {"checkedAt": 1752045724467, "integrity": "sha512-mIbX0LCs+PN85ceuA6NMTFMUNDRRALwLwBy1KPDA0xoAPZGvTVOopvPltVcZBVO5ZJg1mSQ6y8Qsu5GHq9dMVA==", "mode": 420, "size": 445}, "build/npm/types/util/timestamp.d.ts.map": {"checkedAt": 1752045724479, "integrity": "sha512-V5ka9ORpsMwuw2zr99WDonMWfN3jtl/S7qJpqmLNC/C4358vjAvAqhm6ktPfBpsJRuot5OzWBzZa9zGYT0N/jQ==", "mode": 420, "size": 253}, "build/npm/types/eventBuffer/WorkerHandler.d.ts.map": {"checkedAt": 1752045724479, "integrity": "sha512-l6hVUXtYNdNruXsx1c/6hDEHk3cP6JBdYzxT9URdT2Q0JYQKMqllORYzPzr9dL7fG2T1nRKrid2LRdarryaaFA==", "mode": 420, "size": 579}, "build/npm/types/coreHandlers/util/xhrUtils.d.ts.map": {"checkedAt": 1752045724480, "integrity": "sha512-W5XEZC2uhg9UOWedaxpRW/1vxirtEGcD7qZ8XJSvfjQrKAmkPIjkUKmAIYf53CARcnp5o9Ppa0DQznNS+d5Euw==", "mode": 420, "size": 802}, "README.md": {"checkedAt": 1752045724480, "integrity": "sha512-GRhBp/kWc632MfZAV4r6CufH9KWBL9fsVfdhxRo7RLr4ykjyMDii8C0+JdRfUuCK3/5uiJg1+Fo+LieCeqObaQ==", "mode": 420, "size": 14329}, "build/npm/types-ts3.8/coreHandlers/util/addBreadcrumbEvent.d.ts": {"checkedAt": 1752045724480, "integrity": "sha512-A6LSQCDpuhPqx2BeTr0FRbLuAf/1xYtxGaYJwXTF0A4GIJWqu4A9NrMXlC3CIWflGsS0l0U1DFeOOlaYkAJkfw==", "mode": 420, "size": 291}, "build/npm/types/coreHandlers/util/addBreadcrumbEvent.d.ts": {"checkedAt": 1752045724480, "integrity": "sha512-qx/ma5BwL4/j4MCFjr7UMpjPgz0d5BbD9btz3s6BZl8Yi7fG3UJbKofZNej0Rn0FETb0AZ/nn7vsGQK0+GyNgg==", "mode": 420, "size": 293}, "build/npm/types-ts3.8/util/addEvent.d.ts": {"checkedAt": 1752045724481, "integrity": "sha512-e7pwI6LGtDphRV3sOqVd+7Ast54IscyCJBfwIrmdr2OzbmmeuEAiLy3/HDIe6evJrYZCMS+1MFISnprkETj4cQ==", "mode": 420, "size": 1177}, "build/npm/types/util/addEvent.d.ts": {"checkedAt": 1752045724481, "integrity": "sha512-Xy4HBx4v4L4fm5uSXW6y0f8JhVcYSu2fS44vfvtFq3/aLPw654xyb0nq/Q1+oWbsHrhLYD0BnhMrP6MkVPj7CQ==", "mode": 420, "size": 1161}, "build/npm/types-ts3.8/coreHandlers/util/addFeedbackBreadcrumb.d.ts": {"checkedAt": 1752045724481, "integrity": "sha512-/7QfdDaRURQ58tj98SdAk3H7duK9ymZr56D8SQbnNODUeu9BsyKGkbB3gysSjeUQzJzK7m7t2omtLQySPUmRag==", "mode": 420, "size": 307}, "build/npm/types/coreHandlers/util/addFeedbackBreadcrumb.d.ts": {"checkedAt": 1752045724481, "integrity": "sha512-O4jeBn0KLIETKRdFSpfOjGwGW7XjGJ55vKFnV5wqwFshGOUJfGax7gBoFZLgpoD0XIOL0As1OVSIukyAkyXeLA==", "mode": 420, "size": 309}, "build/npm/types-ts3.8/util/addGlobalListeners.d.ts": {"checkedAt": 1752045724482, "integrity": "sha512-aQDvgx2hn7v8qouldY4cVlLgnrw8HoWPo6iLf2P4VDLg1gV+mFhz1Gcw/uVMintignwHo5i7QW1uiF0+AEH+OQ==", "mode": 420, "size": 230}, "build/npm/types/util/addGlobalListeners.d.ts": {"checkedAt": 1752045724482, "integrity": "sha512-pAEBNrl73kMA92emri2fFCNQ9f8iQd95g8NFNlU4fYr5qKT31eIGB3JMQoqJCg64wztDiCxak5vfiz+Qk13rLg==", "mode": 420, "size": 228}, "build/npm/types-ts3.8/util/addMemoryEntry.d.ts": {"checkedAt": 1752045724500, "integrity": "sha512-r+ECB9/aJuHepfG/7tJcwYiRrcyTVwsvqy7jRoS7/hLarwvkje4XQuhdBdsh2+Wm6wYH/Mq7UTo+/NnDrC/2jw==", "mode": 420, "size": 335}, "build/npm/types/util/addMemoryEntry.d.ts": {"checkedAt": 1752045724501, "integrity": "sha512-TLDcHIa07Mok9lVM767e9HxayUVZ2mKClEfpbTAJtVb1NiunD2MpWYBIpfXZ7vvXtjJw2wp+dTwBxd6I/xcsdA==", "mode": 420, "size": 332}, "build/npm/types-ts3.8/coreHandlers/util/addNetworkBreadcrumb.d.ts": {"checkedAt": 1752045724501, "integrity": "sha512-yPvPYEk40P5+aLvpOvOEBDB3sS/b6AuvJU5exd2xVRk4KZt79qoJ1hkIvyuxlsfDe5ftMHtzz1M5IWTuijEPNw==", "mode": 420, "size": 324}, "build/npm/types/coreHandlers/util/addNetworkBreadcrumb.d.ts": {"checkedAt": 1752045724501, "integrity": "sha512-FKMDqW7By6qw5MbIv0EdXfmvgQHHoPa2xmkdaCz4YLByVUxGtx0tsksCEzQtj2wm9bMi2j8QXz6++BC+Ot4OWg==", "mode": 420, "size": 324}, "build/npm/types-ts3.8/session/clearSession.d.ts": {"checkedAt": 1752045724501, "integrity": "sha512-GfIY/47FgW5cZ4YAPfLyx2tEE3umaTfJs1le8u7hDm/izAS1NUHe1Tc9dFfzo80KBfN/QmhFR2yJR+TzUXMIqA==", "mode": 420, "size": 259}, "build/npm/types/session/clearSession.d.ts": {"checkedAt": 1752045724502, "integrity": "sha512-5/aMRuoguuMNK28J/FCrHBIHfuJB3iXIRRzWvv/IbbE7C0OPomWHj5qzQvMpGTTDiJJPjeCOtO3LC4woHD+IXQ==", "mode": 420, "size": 257}, "build/npm/types-ts3.8/constants.d.ts": {"checkedAt": 1752045724502, "integrity": "sha512-UEvBcJL1KhoUxKoCIZbMimyirB18kr7dHVVMyiZ66dwhaarhFi5PxP+pW4F3iQD5aOzo8+URcgOjlZkci29iNQ==", "mode": 420, "size": 1562}, "build/npm/types/constants.d.ts": {"checkedAt": 1752045724502, "integrity": "sha512-JlzBaurtw8cV+i4Np6kypvKULDLv94pPLps7TdJHdv0FYqxQamZtgL/y1Ac/Zae4cDByURNsmzO5NL6E5hiB6Q==", "mode": 420, "size": 1534}, "build/npm/types-ts3.8/util/createBreadcrumb.d.ts": {"checkedAt": 1752045724502, "integrity": "sha512-Ni7vFcuLE2VTitRYsNlumc4WTvSwN9TOOjYs0QtIeZc5J3p2N6rVi1Win8zOqQJWbdcB6fXj+kKtcPzXubUijA==", "mode": 420, "size": 378}, "build/npm/types/util/createBreadcrumb.d.ts": {"checkedAt": 1752045724504, "integrity": "sha512-IXQVggtHUhQqUor3ENDYYdHs+HkIV4yfYpPJF1J8uwwdS3J1MBltJxqcB4ltLJsk0qm059KRnjg4WZZS8DyHtw==", "mode": 420, "size": 338}, "build/npm/types-ts3.8/util/createPerformanceEntries.d.ts": {"checkedAt": 1752045724504, "integrity": "sha512-/GnQ/giopQ1TlnVMlLaqQKBC9iz4I+UK3fjjOpP4G1vSPdeyajgysOihtJOLXHwUcTQQqIgoVn3nerCuFOB8vQ==", "mode": 420, "size": 2205}, "build/npm/types/util/createPerformanceEntries.d.ts": {"checkedAt": 1752045724504, "integrity": "sha512-m6iCuDef6LybXJVUjcZhvBI39X/75bbrjHbDp9gxLzvwE0cnxLWdamlM+GNGFXcMzDJh0mH+5GlW4f/Pbx128w==", "mode": 420, "size": 2153}, "build/npm/types-ts3.8/util/createPerformanceSpans.d.ts": {"checkedAt": 1752045724505, "integrity": "sha512-b/SulSkcGvWXE226ArM0dIfgujP9cgIRPWpdaZnupdGbWVOfHHV5ntQF7iSb7KkYzpYg8iRb64LlSZUm3jocVQ==", "mode": 420, "size": 368}, "build/npm/types/util/createPerformanceSpans.d.ts": {"checkedAt": 1752045724505, "integrity": "sha512-pBBQ0P5wdWHPEh+F3GN7D0t8zxpmmQXkMEqIAgNsTgQJNhmyOi9hm4Ic/QAw0eyZqSqbe3f64AAWEpNz7bk6Vw==", "mode": 420, "size": 366}, "build/npm/types-ts3.8/util/createReplayEnvelope.d.ts": {"checkedAt": 1752045724505, "integrity": "sha512-4Z1JrqtwovOKw+1mGpO8XmWK9z07wnM60DD4P4miTkgxIlAHAiP7nXMSrYfm3ZlrBg+2T38PfhriHWZw9dCJRA==", "mode": 420, "size": 441}, "build/npm/types/util/createReplayEnvelope.d.ts": {"checkedAt": 1752045724514, "integrity": "sha512-MnJcQ89lK9fUyDpn125s7qMoQO/R/8GFDOmEOeBO/45bXVbzNrmlWa2Ru9bFLGVu1kRFvQjziXUHceAssTcDDg==", "mode": 420, "size": 438}, "build/npm/types-ts3.8/session/createSession.d.ts": {"checkedAt": 1752045724514, "integrity": "sha512-cFKXrPLzaGLk88kHaJ/u6NEU4ip2MafNuxJAUpzbRIitJWIMzAIp794KLNZKp7qEBz0N4d+06j8FKYI9MXmclQ==", "mode": 420, "size": 719}, "build/npm/types/session/createSession.d.ts": {"checkedAt": 1752045724515, "integrity": "sha512-MYST5lximn6hRhdsDRqkBvDbjQh/P4cpvqznyfwHIh8Nl4hFcCkKBH+WmESecw0onpn62JcyZBjR97cnnDoxnw==", "mode": 420, "size": 709}, "build/npm/types-ts3.8/util/debounce.d.ts": {"checkedAt": 1752045724515, "integrity": "sha512-AW9KXmEQiG81Sc0aNecfANcxt0NViv7rzLTDfWcuQijI0qGZ+YG4sp+tdj0Fra9+6j02wXOxBXdAZpEO6ThmOg==", "mode": 420, "size": 1519}, "build/npm/types/util/debounce.d.ts": {"checkedAt": 1752045724515, "integrity": "sha512-JNUCLn9aIiyfP0Zqbwxa7d4FI9ba2j8EvEHLMCk43mYsLsj0xD/83c6yefiOBpTbOK9CGdAZPLHS7OBMOm0jCw==", "mode": 420, "size": 1488}, "build/npm/types-ts3.8/debug-build.d.ts": {"checkedAt": 1752045724519, "integrity": "sha512-xcA+QkShSG3dSr60fuiWe8dh+MDrVunTFIzMKiExTS2P5TeXGBHfWg1UEHH5ekB78/Rs3p0B+wX+La5zAKJNXg==", "mode": 420, "size": 397}, "build/npm/types/debug-build.d.ts": {"checkedAt": 1752045724522, "integrity": "sha512-40iKxvZW01zAim/rsL49d4L5mitLBs6CYoKYLnxjU00Xm9O6zhTw8/Zccf6eO1OCwfpgN1XmRlT19qfxfU+YNQ==", "mode": 420, "size": 389}, "build/npm/types-ts3.8/coreHandlers/util/domUtils.d.ts": {"checkedAt": 1752045724522, "integrity": "sha512-Zye4r0fGwR/aExLuHIUwW7smK7KjkFQetlz1KzeOPpT/TYQtb5da8uk68Y7lyISRg7nb44qMdJJouwD41YwfBQ==", "mode": 420, "size": 787}, "build/npm/types/coreHandlers/util/domUtils.d.ts": {"checkedAt": 1752045724523, "integrity": "sha512-/f67U1FgNwXA15Lttn79xGScyHAaU6ABJwiDrz9SiB6RB4Tb4O6hvnxFCcCud8XQ69NRZp2hU//80pgymm9L6g==", "mode": 420, "size": 776}, "build/npm/types-ts3.8/eventBuffer/error.d.ts": {"checkedAt": 1752045724523, "integrity": "sha512-3PMGu0AP9YrUxu93fmhIfabJORMuJ5c5ID57rjEfyQVuRxI8Mes/Xa22bVBWrAIGrP71gKz8bExEjenhrw2akA==", "mode": 420, "size": 204}, "build/npm/types/eventBuffer/error.d.ts": {"checkedAt": 1752045724523, "integrity": "sha512-4ARCPgUPBKxiGGdiVpyx3KPBeBJl2tOuUt5yXbaxwnfUix/wSx+rQ0zv5umN34tl8MxJ542j6D2lNZ8PkzrbiQ==", "mode": 420, "size": 198}, "build/npm/types-ts3.8/eventBuffer/EventBufferArray.d.ts": {"checkedAt": 1752045724523, "integrity": "sha512-MfG+i3w3Qkc94gPnhq7F+QRFWhRjBcWMDFCMFAIfG3WOdBW32jzb86zlpooUodCQQND/7Uibaexi1ir/ZUUIdQ==", "mode": 420, "size": 954}, "build/npm/types/eventBuffer/EventBufferArray.d.ts": {"checkedAt": 1752045724523, "integrity": "sha512-R5H0eyHxfTTdeozYyFFrTl0vgIzQKjTltpruDW0xxDTQ3IzFFRELCahAN+mIm0/BzCvkf/rqF1Cit9DG5XcF/Q==", "mode": 420, "size": 928}, "build/npm/types-ts3.8/eventBuffer/EventBufferCompressionWorker.d.ts": {"checkedAt": 1752045724524, "integrity": "sha512-bjSSHgTCcPn5vRGBaqfGTMvgY4pmsIUN+YlG0bA75/l208CHCzdY5F/e9uIB+jP6/HRFynbLkMNB623pvojK3Q==", "mode": 420, "size": 1587}, "build/npm/types/eventBuffer/EventBufferCompressionWorker.d.ts": {"checkedAt": 1752045724524, "integrity": "sha512-vgBANdPhvQ6yXMmBMciHEoW0yTFNUce5CB9V7SmJ6JWvsZ2ga+anWhxsTIxkL4uCKRhp/QfuN8gxgQFS2s+Tjw==", "mode": 420, "size": 1544}, "build/npm/types-ts3.8/eventBuffer/EventBufferProxy.d.ts": {"checkedAt": 1752045724524, "integrity": "sha512-mbu0LZR5zTprc4LEOFjbcp76px7yUnLqfS1OGXbFyPYS02bGBiqgyp6Z0jrq5E+wszgzxLwV4laAFTaruJ7OUg==", "mode": 420, "size": 1501}, "build/npm/types/eventBuffer/EventBufferProxy.d.ts": {"checkedAt": 1752045724535, "integrity": "sha512-mnWLjnPFMxikKwiUWdLCwuDAvzSrh+ID6fGWkB1q3T0cMI3VireTt2UXOkWkMUMvyW0C6lTK0//TvGiS/pz7PA==", "mode": 420, "size": 1518}, "build/npm/types-ts3.8/util/eventUtils.d.ts": {"checkedAt": 1752045724535, "integrity": "sha512-GTYb0LmshH1/T+kzb7ldnQRBjoOqmQ/+OTPKR7quFqKeyBdMtcoWjN+DvG1fbpNIVUidTmxrJzz6h1Fk1/Y7Dg==", "mode": 420, "size": 620}, "build/npm/types/util/eventUtils.d.ts": {"checkedAt": 1752045724535, "integrity": "sha512-q3OcOWBtcmuazoqjRUFd8g/ubYExUO2qflRtDMiWbod9FEiP0YYNbK8cp+W+5gfpiEnw6/DnB5+2dWV3Dbzi1w==", "mode": 420, "size": 614}, "build/npm/types-ts3.8/session/fetchSession.d.ts": {"checkedAt": 1752045724535, "integrity": "sha512-9kMSVRmS95R+phEnXT/MUtoYn/d7GDQujWx/SS5DJlLzJAItSX/Q7d6uRWwQ63+LHTxzJ+udqcNBhQnVa7eyeA==", "mode": 420, "size": 183}, "build/npm/types/session/fetchSession.d.ts": {"checkedAt": 1752045724535, "integrity": "sha512-cuU1GsEK/zcmQS1pYcLE3oe1N2MzsxHvosgBWBneFEbJhvfs0irnhoOGz4qo2cSSHR8Js4chvLvRRm51JSBqrw==", "mode": 420, "size": 181}, "build/npm/types-ts3.8/coreHandlers/util/fetchUtils.d.ts": {"checkedAt": 1752045724536, "integrity": "sha512-5Ew22PW1dUJG3CcmnFNZmIrnKaqAACf9R5FcRVJk5XrL9JZ8nclbf5JidDB6wcsiQoVXUxBZun/An/N00PoW5w==", "mode": 420, "size": 1215}, "build/npm/types/coreHandlers/util/fetchUtils.d.ts": {"checkedAt": 1752045724536, "integrity": "sha512-AWVLgV17H/BwLR2VoR1t+vmGphppWH5SMvyDM0LzSbRKxBtdEUss7pqTIuwmdjYdHTBFDk4IJqaL45RUIvu+qw==", "mode": 420, "size": 1202}, "build/npm/types-ts3.8/coreHandlers/util/getAttributesToRecord.d.ts": {"checkedAt": 1752045724539, "integrity": "sha512-+Ev6yXPqdMd177gdU40/tbi9IJw4nbOgeFqGzIRCnj3RbHb3x6FQtecjUyGlYMU78vGLsub5KWIJmJUbqrDkjQ==", "mode": 420, "size": 250}, "build/npm/types/coreHandlers/util/getAttributesToRecord.d.ts": {"checkedAt": 1752045724540, "integrity": "sha512-IYTjPsY80IPOSm06ILcVPfT4vIfj0yxNAzqdmS0yL7UK8vN/1QEXLvrMU2pg6oO2DzMx//vH0If02HTmYyQzlA==", "mode": 420, "size": 244}, "build/npm/types-ts3.8/util/getPrivacyOptions.d.ts": {"checkedAt": 1752045724540, "integrity": "sha512-KjuHNmb3184XjULsc/LNWTTLwlgg2iFGkCXjH+DlmB814kQHLGg22mrIZM2M1hSCGMATWJohUco9+ZOk5d2zSw==", "mode": 420, "size": 686}, "build/npm/types/util/getPrivacyOptions.d.ts": {"checkedAt": 1752045724540, "integrity": "sha512-3e8AfktE7+KuNcfzEjogI+6vJU+18VX5Mb9CYpm7W9/tDu0nHaJNkbRs9p2Mzf+8PJPBtxf0X9/PnbvlrC7f3w==", "mode": 420, "size": 625}, "build/npm/types-ts3.8/util/getReplay.d.ts": {"checkedAt": 1752045724540, "integrity": "sha512-oun/PtnPy0vWwnFUHDnf6flll9Jonggy/spCzuaLgHnCtlCYAUV/5YvWhqc05MjRFqy7xsdRH0RluDJLXDLl8w==", "mode": 420, "size": 275}, "build/npm/types/util/getReplay.d.ts": {"checkedAt": 1752045724540, "integrity": "sha512-GASBWtLc3jNoWmhUQsr2E3sLvgbMgWnoi0jLNEzVuXN7u8f8Q2IP0/E90L+SN9W2ikScSrUwJj9I0ssQLrutwg==", "mode": 420, "size": 273}, "build/npm/types-ts3.8/coreHandlers/handleAfterSendEvent.d.ts": {"checkedAt": 1752045724540, "integrity": "sha512-c4fnJcTEh+EtEA/iMVSCTBgL+5v2UJ+sK4hs7Xhc4le0zpyzfI0usnTnWM8xI/5AO0wd0AoxmVtD9TvIPenLWA==", "mode": 420, "size": 467}, "build/npm/types/coreHandlers/handleAfterSendEvent.d.ts": {"checkedAt": 1752045724541, "integrity": "sha512-OBb8hoFkrz9wTTqLbKX53/qrHe/b0TvThFf/Ru7MTjAjcC2c4xx6vT4rH2o+QLs7jhWjJHWWNN7Eylp6J887kQ==", "mode": 420, "size": 467}, "build/npm/types-ts3.8/coreHandlers/handleBeforeSendEvent.d.ts": {"checkedAt": 1752045724541, "integrity": "sha512-Rx7JgldOyaOr5OVsnjtL4qUfCSRUST7YZofb9P74zC3J9Fk3n2v2ozr8bsGlYmfOP6DA85BOp5ZPniIi0eWvcA==", "mode": 420, "size": 397}, "build/npm/types/coreHandlers/handleBeforeSendEvent.d.ts": {"checkedAt": 1752045724541, "integrity": "sha512-cVzgcu0bvPFXV1D2IaSbuPOwi5sVqVYjR99O/ntT5jUoBxNEQ1x/K4YH1/IFjkTs/Fh8+KLaLGnAgtlpaIOHMQ==", "mode": 420, "size": 397}, "build/npm/types-ts3.8/coreHandlers/handleBreadcrumbs.d.ts": {"checkedAt": 1752045724557, "integrity": "sha512-77PNLs0/p+FZcxJDUWBOIqlA7ZiW04hJaHLYF5JKK76EyGRqXriC//i+gty9lTvrVDSvPdSdgQEAmDj9EnBOyw==", "mode": 420, "size": 784}, "build/npm/types/coreHandlers/handleBreadcrumbs.d.ts": {"checkedAt": 1752045724557, "integrity": "sha512-dZMwU2xpmJPcHVdmGvefVW1hEWPDH5a7dUjo2SfcKaBdxPmPNsNwz/loOvxzJfyFLlJYMdh4h00c/YWy7nFxiQ==", "mode": 420, "size": 757}, "build/npm/types-ts3.8/coreHandlers/handleClick.d.ts": {"checkedAt": 1752045724557, "integrity": "sha512-9uGx2gHMaTQ7Hl72C9vpP6iaI4n4qRnMgtRgv7INDriK/nssU2MVjsNb5Ii8MH07ouJ49xrzi6i8yjOhKDfy3g==", "mode": 420, "size": 2200}, "build/npm/types/coreHandlers/handleClick.d.ts": {"checkedAt": 1752045724557, "integrity": "sha512-Uy+qktlqF/JLEoWyuXeuvmFp5Hkt29VRFZwBr1sAqePhbJVTGqKejeRJwv4DMD17ld8JjNtOgkl4gEt51b194Q==", "mode": 420, "size": 2162}, "build/npm/types-ts3.8/coreHandlers/handleDom.d.ts": {"checkedAt": 1752045724557, "integrity": "sha512-eJpSReYpYQ7SuAeh98ZQdEsTyiZV6obNgIAMRlSQ4qDvEI1lHCdC2NmCoozp9mjRP/HTLTI2KJdRfY+7HeOzhA==", "mode": 420, "size": 553}, "build/npm/types/coreHandlers/handleDom.d.ts": {"checkedAt": 1752045724558, "integrity": "sha512-h1BBtZz+MYTJSi0w5h0mpU7kRw9UegdkN7+fZGI6lTu/ScjsScZQ/oe9HaO4RQ2mZE9m3voniqkf2wuhbpJKKQ==", "mode": 420, "size": 551}, "build/npm/types-ts3.8/coreHandlers/handleGlobalEvent.d.ts": {"checkedAt": 1752045724558, "integrity": "sha512-fl/eb3TgGXnODzEK0fyGo1YPVbYBfEe912cx3HoL7cQXFUtTtRF0pvUNePz9hNJ35zny7NyiuTddsqvmX/c72A==", "mode": 420, "size": 350}, "build/npm/types/coreHandlers/handleGlobalEvent.d.ts": {"checkedAt": 1752045724558, "integrity": "sha512-qjb5KrwSk7vW/WnRc/AbvYvH1MNE+01hD8rIg9q1Zb/N1Jr/emV7r8Dmk3uQz1b3CWwDVdhtR79lKGuB4S10Xw==", "mode": 420, "size": 352}, "build/npm/types-ts3.8/coreHandlers/handleHistory.d.ts": {"checkedAt": 1752045724558, "integrity": "sha512-mXSOb9qK1Wtvx5Ic4LRvxhSrBr+o92/xczNoolIahN/zIQ6WaImp0jpJp2r7qRLf+ZwRgRf8BTepYwYfMq8oNw==", "mode": 420, "size": 357}, "build/npm/types/coreHandlers/handleHistory.d.ts": {"checkedAt": 1752045724559, "integrity": "sha512-QzpVDokRUnw/gQqZqJlrce9l1DpB5cc+NVSmKg5oweTmLu6vFb6cmm3mKHFXSfBQQ+iIUKZXSuhV/OjKz6UXAg==", "mode": 420, "size": 359}, "build/npm/types-ts3.8/coreHandlers/handleKeyboardEvent.d.ts": {"checkedAt": 1752045724566, "integrity": "sha512-3T5GK0G33+7V8TzHRQ+Yu0DHGKZ/qOgGNWxyvb2maWokpokYHbCVBEJkodmTPjq7r1yZ6gYUDFXP2u3hausLuA==", "mode": 420, "size": 414}, "build/npm/types/coreHandlers/handleKeyboardEvent.d.ts": {"checkedAt": 1752045724567, "integrity": "sha512-kZZotJkZMfsM6C1odcthrcpZCX5GayNzf+gxODgMA7mkNQ0So7eyfNom31fqjT/gNp/eUiPTJ/R12AWzp8jjxQ==", "mode": 420, "size": 416}, "build/npm/types-ts3.8/coreHandlers/handleNetworkBreadcrumbs.d.ts": {"checkedAt": 1752045724567, "integrity": "sha512-Gy05Fa5gNmG3fJWREIKSk1DfAmZRyKffGwvBQNdZ+H7++rsT8dtHBk0cz/90In9+cmqTQnM4zS4nDV6vKxex2Q==", "mode": 420, "size": 840}, "build/npm/types/coreHandlers/handleNetworkBreadcrumbs.d.ts": {"checkedAt": 1752045724573, "integrity": "sha512-WCKPEkOygLdwJEXgYeWDk9raaqza4fmRqqSVWmD9aXA7qn/ZSE7JmX4uogTj0uAp2ptB0hkD+hRbqXsiV0QNqA==", "mode": 420, "size": 833}, "build/npm/types-ts3.8/util/handleRecordingEmit.d.ts": {"checkedAt": 1752045724574, "integrity": "sha512-O9v94wkj4wSxGN0RkkS3hRmOF1AO9I4fSj/Qft8LDLdEJbFmUtfBAsy8A/Lxr8qEygoxMZN+ppjL2Vl3bv072A==", "mode": 420, "size": 595}, "build/npm/types/util/handleRecordingEmit.d.ts": {"checkedAt": 1752045724574, "integrity": "sha512-uTe4MIC00KFlHT3cljb+lOXV4UJdAt0+OwaWlEOA1PkOL42xuS7wTRxfXwLyFRGEJrm68NfUOcPCpAYuAcyyKw==", "mode": 420, "size": 585}, "build/npm/types-ts3.8/util/hasSessionStorage.d.ts": {"checkedAt": 1752045724574, "integrity": "sha512-uUZEmzN214/dfs9bkWuqxRtpVNMFZ8RPrfidmavMSCHZIoD8wJZ5EPnP2I4ohdqe6PuVdgC3s4Orjo/WQOO4RA==", "mode": 420, "size": 144}, "build/npm/types/util/hasSessionStorage.d.ts": {"checkedAt": 1752045724574, "integrity": "sha512-+/29XJzM8VYbwbrvbbnf5qLgQkxI0Ciq9Q4pbDol2Vorfo/5MhJ1mzf1QhNOtMY9qWqqEfF/agoIHUuyx+WrZA==", "mode": 420, "size": 140}, "build/npm/types-ts3.8/eventBuffer/index.d.ts": {"checkedAt": 1752045724574, "integrity": "sha512-osyTBA6et10p6C2vdlfD4O1K1LO6miprpa76yvBjV5wRsmYOrjo/b2xsYGL8DIJxISx8QZWg1jaAI2xqIXFfqQ==", "mode": 420, "size": 366}, "build/npm/types-ts3.8/index.d.ts": {"checkedAt": 1752045724575, "integrity": "sha512-Zv0oi/HP43uBu/qR51khYe3UbRnQBTVH6xH/pN21lwsMm3iRQ8/9XLCbL+0isPaKB770IaNkIO8IqsGCaunD5g==", "mode": 420, "size": 414}, "build/npm/types-ts3.8/session/index.d.ts": {"checkedAt": 1752045724579, "integrity": "sha512-2whiDQJLXoH1s2H4gpViuNFnZuU7QLDEjh5FMTZeczdUHYVVFzt23ttnQFOa1+kHQE2+w3crc+D4A080rmwv4w==", "mode": 420, "size": 71}, "build/npm/types-ts3.8/types/index.d.ts": {"checkedAt": 1752045724579, "integrity": "sha512-tKGG5mlYZ1bFldvVcqrdhovh3rNWfL5oVOxdMq0xD1GQQFe2xIq2WYzW/xCUeV1w/FbHGMKLfSMLOxf7sD5fRA==", "mode": 420, "size": 182}, "build/npm/types/eventBuffer/index.d.ts": {"checkedAt": 1752045724585, "integrity": "sha512-luwfuGI6QDH2fY/OO1Q6zIsoZrLPNg5OnZuIGaSGu/LaYHnnR60+CsjFmi70K8Kg+ocX2dZQwPmuHPLZ9teYdg==", "mode": 420, "size": 359}, "build/npm/types/index.d.ts": {"checkedAt": 1752045724586, "integrity": "sha512-shcBRdiN2sh96ZBYgyxh54hU6b4u9MW+q5e5qjfNsq+YFHraTwZco2gUw1mDzmlWYorGuExXW5/44u5c9x+vWg==", "mode": 420, "size": 414}, "build/npm/types/session/index.d.ts": {"checkedAt": 1752045724586, "integrity": "sha512-xov2QY8LfgrwPG6RvUur9ka+wUDeJbr41GCgAKHuCApB+Xt88cmgCDrx6davJmnGYxJW5nNtjQIQRzUosEUExQ==", "mode": 420, "size": 68}, "build/npm/types/types/index.d.ts": {"checkedAt": 1752045724586, "integrity": "sha512-JB33uMbZMsP55LJVCJJot1Gc0edRIeLb2Qvctd64ykSMuu1XsqC8ENATUmYThNhF5WExcmeDFD2AuqJ0+jIByA==", "mode": 420, "size": 175}, "build/npm/types-ts3.8/integration.d.ts": {"checkedAt": 1752045724586, "integrity": "sha512-lvBfQ+2/POMnIamNVgVqF6EJfKzZvqi68WlwZ9BDm3SLvsDrmytAeiEWcH+yxoFQy+m7dbOKndZDygcvZ9Gvyw==", "mode": 420, "size": 3572}, "build/npm/types/integration.d.ts": {"checkedAt": 1752045724594, "integrity": "sha512-V5+pZ4JjnS/oEyKWoNMClTMyUtH65lXCto9AolGK0QJwHOqQUQgX26sr/VL0AahovHUg9XAN3IhoalV6pvoM8g==", "mode": 420, "size": 3552}, "build/npm/types-ts3.8/util/isExpired.d.ts": {"checkedAt": 1752045724595, "integrity": "sha512-cc3SQnOue1ibnlzX7rEfxk1ACf1iOTgF7rgNr0c1yhwSJ6txkc/QIxg0iSCXbmchMbgDv37sDRHH57i/+QJl/g==", "mode": 420, "size": 295}, "build/npm/types/util/isExpired.d.ts": {"checkedAt": 1752045724596, "integrity": "sha512-ZZ/dD3E2twTxWPk7yFi7b8kfxTlf9nttKYPchNeHsQY5I2jdabKrv+yxMD2ktsrZv8Fu9V/AdzBtIdmNN8vx1w==", "mode": 420, "size": 288}, "build/npm/types-ts3.8/util/isRrwebError.d.ts": {"checkedAt": 1752045724598, "integrity": "sha512-Qf/tStHRzwlU6ihA8ISEGk9F9O6Hvjpwpv+VnqDcWsNGMQTJmXOJLKb9r7nZYaXTv/Dd8ThyON9zUAC/pmXHGA==", "mode": 420, "size": 270}, "build/npm/types/util/isRrwebError.d.ts": {"checkedAt": 1752045724599, "integrity": "sha512-429UvMJbolfTLGiP1sE+lLCaK+YejgjMyjAoRM4MfSrh7dNDpg6L+hHj5t5LW1pNCGLxZqzqia/RZK5E6ooR+w==", "mode": 420, "size": 268}, "build/npm/types-ts3.8/util/isSampled.d.ts": {"checkedAt": 1752045724599, "integrity": "sha512-Z+6GZFjAyRILFyXNC5J1n/dCSiNRHhv6p+r62nK+QMrZF99sEW0phYj0uLyuCOOyPAEzd/YBM4mJ5kd0PvzWLw==", "mode": 420, "size": 234}, "build/npm/types/util/isSampled.d.ts": {"checkedAt": 1752045724599, "integrity": "sha512-UqsTh//8s2JJ1ISayG+39NC+2OUZtjKlUMXPy9K7v1906CY8Y2F0wg4kUS3fMGVl/3QgEQooXLNGGhtoWrCRJA==", "mode": 420, "size": 225}, "build/npm/types-ts3.8/util/isSessionExpired.d.ts": {"checkedAt": 1752045724599, "integrity": "sha512-PZSX4pP+ot68cthSNASInpq5raIkuwAKs0Kyn6N2Be6K9j8pwBfhIDX+aluMZ08semtIWEghsV6bDHO7g/fwrQ==", "mode": 420, "size": 356}, "build/npm/types/util/isSessionExpired.d.ts": {"checkedAt": 1752045724601, "integrity": "sha512-ai0uryyruvUuz2qXNILi2cjowxW965lcYqfp1qBQMTpBJRC1J1nI3Jj3ddZuNPMi1tGdPVVxcQ1gk4MsxU1r/w==", "mode": 420, "size": 350}, "build/npm/types-ts3.8/session/loadOrCreateSession.d.ts": {"checkedAt": 1752045724601, "integrity": "sha512-EkXCQ++QD9Y3sGQx0FQO8NfWsBoqwiyJDChym8U3dFauK0InfKBvr+gnkmz5g17W9keb1kDmtbtq6huKM7qjWA==", "mode": 420, "size": 470}, "build/npm/types/session/loadOrCreateSession.d.ts": {"checkedAt": 1752045724605, "integrity": "sha512-ZtGwE+u5Os7CYh6gd0c3f0dfGXxVxQUChw0l0xHtFDbfqCRBuh2AsG88ldqXfUEpcYXfj51mQ+/bQ9S6Abd2hQ==", "mode": 420, "size": 463}, "build/npm/types-ts3.8/util/logger.d.ts": {"checkedAt": 1752045724605, "integrity": "sha512-+WaLuL7njadpOUu7vSfWfDOgpn5EzcUz+S4wxur9zT3P00iGplrDXNJhTHZM2QWbj0Khi2LwJ4IFYIByM6+tjA==", "mode": 420, "size": 931}, "build/npm/types/util/logger.d.ts": {"checkedAt": 1752045724605, "integrity": "sha512-foVQBCGMJ0cqZBiaA8QejND0BKjsqnhUaScQqMgoF+VfeWDL0cXWfnqg2jyO2jg2MShMTKHtZVz8k0bWpASE9g==", "mode": 420, "size": 909}, "build/npm/types-ts3.8/util/maskAttribute.d.ts": {"checkedAt": 1752045724605, "integrity": "sha512-dh9bs2WLZYaNhQkGyVKAMDamtJhj/iW7QOM3mLIXZ8jAgnKYD5XcxtDHrkhnDIarSbV5wA2uS0w74PJnpG26HA==", "mode": 420, "size": 550}, "build/npm/types/util/maskAttribute.d.ts": {"checkedAt": 1752045724606, "integrity": "sha512-b9m837iFPuOGpKLxvHW0mJem+RegGN0el/sB0OFT57cWUJ2U6eHAVmX031kPFslHCcxxwPpPz5Zs6ly9JZCNWA==", "mode": 420, "size": 539}, "build/npm/types-ts3.8/coreHandlers/util/networkUtils.d.ts": {"checkedAt": 1752045724606, "integrity": "sha512-mqlntpugejjKzQqwsK0IIbxPuTMsAyfBv4Mz4j+/AlDGEmcOyT0HcY8W6OSNrRV2/HMwzOUie44bfsktlXgyTA==", "mode": 420, "size": 1964}, "build/npm/types/coreHandlers/util/networkUtils.d.ts": {"checkedAt": 1752045724606, "integrity": "sha512-rJRUQcG6jNk1uUAE9WRgxQSr7EZaJAj8up+M/hj3OabDwMPlC+qrcj07ApN16XEJQrretWI8ghADerjrN271RA==", "mode": 420, "size": 1933}, "build/npm/types-ts3.8/coreHandlers/util/onWindowOpen.d.ts": {"checkedAt": 1752045724606, "integrity": "sha512-eIiCj5R3R1ALnRjZqdxo3Im/D8ytUR+u/vOXD9/xNjOrdFy16IJePK+M5vjJFW//OUDI1nVpPI7ILbVw81H7cw==", "mode": 420, "size": 278}, "build/npm/types/coreHandlers/util/onWindowOpen.d.ts": {"checkedAt": 1752045724607, "integrity": "sha512-pXWJPFPfrL8FjriT9VUXAlcFFRdLlQHFfoWsXjTEwdDUFbHt+8S9ZsMXKTLhqCcHeMlWaTV7sWUr5Ex6Kz3Vqg==", "mode": 420, "size": 269}, "build/npm/types-ts3.8/types/performance.d.ts": {"checkedAt": 1752045724608, "integrity": "sha512-5i3hbwnIZqYX01e/gDAIUUSokUQuLngrYkon8HvsNw5OKiDTKixz/LmgXDxzZDsFYYXw59CRh2KWthPMBSntKg==", "mode": 420, "size": 4544}, "build/npm/types/types/performance.d.ts": {"checkedAt": 1752045724608, "integrity": "sha512-exHnxTp7dCIviBkCa/QAABFKN8z7qBrnBB0DeNELuaikpuOu188H9il9LEiqSMTQ/tmKNfrg7l5ElJc2zbFGpw==", "mode": 420, "size": 4412}, "build/npm/types-ts3.8/coreHandlers/performanceObserver.d.ts": {"checkedAt": 1752045724608, "integrity": "sha512-vRUpa1SFkwfXYCFmkAOZtDVoXAOUi8860gdm4BryFGePQq0uLaiuH0xYjpXigCRGEqFj7rDWNz+cn8pQoC20fw==", "mode": 420, "size": 312}, "build/npm/types/coreHandlers/performanceObserver.d.ts": {"checkedAt": 1752045724608, "integrity": "sha512-UzlLiejq8KzaK8ieDEB4AwdPBOP2S8ERzTMF1y/pLfKtdojNBuA8B8ZlDGaJtLEpmolSYOoy9UjIhX6DUWdoqw==", "mode": 420, "size": 309}, "build/npm/types-ts3.8/util/prepareRecordingData.d.ts": {"checkedAt": 1752045724608, "integrity": "sha512-ff/fgbuUYAbNE6nhbqjxCFWrUhNRqM/gi+c9febFGu30AkwQgoi/zc5c0t1SIVZHv7lb9Rm3VqIOacLfCqR/hw==", "mode": 420, "size": 348}, "build/npm/types/util/prepareRecordingData.d.ts": {"checkedAt": 1752045724613, "integrity": "sha512-8l/6u5b4itqy7tTFYmBQ6EOIlUhKxoBPFDkKue10iZkaRWlUbkf4l2bsNbEmad0sBoopBmUfckL0JhsLvOq7fA==", "mode": 420, "size": 343}, "build/npm/types-ts3.8/util/prepareReplayEvent.d.ts": {"checkedAt": 1752045724613, "integrity": "sha512-g1PLTa8GL0jC1v3tOEOk3vex/Um4F8zYosu0v9+yN0kHwjs27rUTxbJ3+2RtIqa8BbZFo/bCs2JEGwdxqtzZGQ==", "mode": 420, "size": 502}, "build/npm/types/util/prepareReplayEvent.d.ts": {"checkedAt": 1752045724613, "integrity": "sha512-3MGC6IbYH7n/L5wEQgn8fbFG8pVoHFmPocXavcIRGRsVhVb9eZ5Jy9bOIaRRI0HGOz9Mgqr5qDdLMhygi3KD5w==", "mode": 420, "size": 497}, "build/npm/types-ts3.8/replay.d.ts": {"checkedAt": 1752045724613, "integrity": "sha512-f3wXh8j4KnIklVQQz0C3T9QWi57JJL8xYVQhP6vQ13ziiDkMVrz9h8+nV7gQHI2Q5xVLqHf52nVN7x8tz9mtlg==", "mode": 420, "size": 11804}, "build/npm/types-ts3.8/types/replay.d.ts": {"checkedAt": 1752045724614, "integrity": "sha512-XMl2xRFbMyi1IAv9JNHSKl3NYhjs7QUzymtri0GVHqI0jREhsWd9rfLzEB547rEUegQ53KTNT7A5y8ajEapEdg==", "mode": 420, "size": 16253}, "build/npm/types/replay.d.ts": {"checkedAt": 1752045724614, "integrity": "sha512-fmP9zvTRUYRAe1SEcFHkQYEr8f0hGOGxJtt4aBsxgUIVsvc3ZqmCq+Lv9d/au7zVHIvTVLhEKFr7NmB8zPsfiw==", "mode": 420, "size": 11501}, "build/npm/types/types/replay.d.ts": {"checkedAt": 1752045724614, "integrity": "sha512-tSkgVROL3+iCjMafXpxPJyQWQHqslgtkcMM5HqUH+ygOwYCuTrK5Nk9XNLVyvlMGbo+6Rohs7wUvnKzqljJJeg==", "mode": 420, "size": 15797}, "build/npm/types-ts3.8/types/replayFrame.d.ts": {"checkedAt": 1752045724615, "integrity": "sha512-706hcFc3/vD4z+DmC8q06j8dld2zoPzhyOAcTPjxyzzqRao6gR8m8RxAMASlDYPJrWay9Fmk4OWg/AAORRLdaQ==", "mode": 420, "size": 6245}, "build/npm/types/types/replayFrame.d.ts": {"checkedAt": 1752045724615, "integrity": "sha512-z/rHjwgogcgiKLI2UWPXEhV/3RxyWBG5UgzAm7vb6En8A+eornXCYSgHfA25+LPJDK0QJv0LFlKZqr4L7jnsTA==", "mode": 420, "size": 6071}, "build/npm/types-ts3.8/types/request.d.ts": {"checkedAt": 1752045724615, "integrity": "sha512-AQh+VxExkBlvNL3bUw3+Mc4Pm2L99ogzFiDw7/ggQj7pcLsx49lYwrt+lpeM+K5HQDe/NwQB+HQMuxi8jCQUpQ==", "mode": 420, "size": 553}, "build/npm/types/types/request.d.ts": {"checkedAt": 1752045724627, "integrity": "sha512-Z18jUNKjtvmtCCT4ltfWJ9V3iaLaMoZ9U74at7O/NWyJ0CygkVjqtFaQlJVo7cxq0X7Li9zZbRx32rMBOjPimQ==", "mode": 420, "size": 537}, "build/npm/types-ts3.8/types/rrweb.d.ts": {"checkedAt": 1752045724628, "integrity": "sha512-EzoYIvf5NAEFaZ+S1ddw9MHjgFM9KYybrnw2ApLR4ylej4EwTGebs98oW9o4q2lWmb393ntEMPS/8hq6amxdLA==", "mode": 420, "size": 2377}, "build/npm/types/types/rrweb.d.ts": {"checkedAt": 1752045724628, "integrity": "sha512-OVcbAslyLMWbme26DuRWM7rs2PUNPlycJh7qA/orj54N68hoa+zJDIiU3AJHGu9QLFum9rHMdHYnMUx1MFTIHg==", "mode": 420, "size": 2313}, "build/npm/types-ts3.8/session/saveSession.d.ts": {"checkedAt": 1752045724628, "integrity": "sha512-kLfUueXK2fAcq7FVQWAtOhksrO/rJMv3s7U/n/SO37nXIFxPXY/9VKr/FIRz0gf+4vtxZg/swgjRf3zo5E1iCw==", "mode": 420, "size": 191}, "build/npm/types/session/saveSession.d.ts": {"checkedAt": 1752045724628, "integrity": "sha512-a85NLWpQJ3mHTJVCRXnFVu7rf6SBlZ+9ryyGCfF/kHT79dw+x+Kt6go8gWMZJRH7UuOpnFpDD5hO8mNEuP136g==", "mode": 420, "size": 189}, "build/npm/types-ts3.8/util/sendReplay.d.ts": {"checkedAt": 1752045724629, "integrity": "sha512-Y4GcMX703qHaq4khXMgQKX5tQ52WZsDotSvuTviHn4UNQJomNRU5+EyAmek7IOMgvdgn41KSl+pkiPy0WQh5pQ==", "mode": 420, "size": 299}, "build/npm/types/util/sendReplay.d.ts": {"checkedAt": 1752045724629, "integrity": "sha512-S5Lg9AiOkuUeeDITNRnQMgx5kGfQecU4BeQYA9rGE/MOMP0urMFuefnWVFDHvB6ViERIOXODt4eewmLplDVomw==", "mode": 420, "size": 294}, "build/npm/types-ts3.8/util/sendReplayRequest.d.ts": {"checkedAt": 1752045724629, "integrity": "sha512-ejKs67hledWDIjxP91F+dNXTeqzZPXz3P/Vw0ASu5rOkMfpru/MC6BWGxsPUJAshyCaxgCQM59aksrlmBWdsTg==", "mode": 420, "size": 829}, "build/npm/types/util/sendReplayRequest.d.ts": {"checkedAt": 1752045724629, "integrity": "sha512-w5C7krRAylrF9Mi0p0pCU7ZUXyizlgaGJIi5KUeZ8J/wZg/fYc5viX2HTWOD47/Hkse3mLzSJhkcBhK5rp+Wqg==", "mode": 420, "size": 822}, "build/npm/types-ts3.8/session/Session.d.ts": {"checkedAt": 1752045724629, "integrity": "sha512-PH881rSA9wsn7wFjTSO0JZE7I55PvEGDhhlPRlEqgqx037CnkkqRMhpE5T5qYXqI6BYfj+4MH4rXwrkhsDz+dA==", "mode": 420, "size": 251}, "build/npm/types/session/Session.d.ts": {"checkedAt": 1752045724635, "integrity": "sha512-OV1c/audjdGYPwbTlvn0QdYoDHZ/cs1o0OT5ob8xr6ZStuy15jTT7W3pBvWu6u0jVhyHaLLJkx67b2MnPsJ17Q==", "mode": 420, "size": 247}, "build/npm/types-ts3.8/util/shouldFilterRequest.d.ts": {"checkedAt": 1752045724635, "integrity": "sha512-wTBC1kX+y4YmyURr3hzCpgLabbTdLMhzmOYFPWQYdI7sH7CqcJy2KB1ggPMbKmaAI0RwzBg/a6QugGm0i1nnyw==", "mode": 420, "size": 313}, "build/npm/types/util/shouldFilterRequest.d.ts": {"checkedAt": 1752045724636, "integrity": "sha512-4<PERSON>hl+d+9OMq9n9zgrPU9xpgCGHIOVAG44e6P3ihu+t0b/wTL5UDZm36TWzsGIe5YwdjUH2caL6zz6udrwnWRA==", "mode": 420, "size": 310}, "build/npm/types-ts3.8/session/shouldRefreshSession.d.ts": {"checkedAt": 1752045724636, "integrity": "sha512-IT9SkyVw1MQs9kkdoGi2Jf8O2nmhDEuk4sk4Wejkwn7RS3+D6a4BwTbsYjlIh9MI2dMPmsUrE3YSj+6xIlU7BA==", "mode": 420, "size": 326}, "build/npm/types/session/shouldRefreshSession.d.ts": {"checkedAt": 1752045724636, "integrity": "sha512-YgXQHhkqQVwQ2pFWhgru4D3n+n0+5P1R8xEw3kdL4vZgCYynxHLRYJUzMnKnOAFEFl9B4GTAltJjbDs3U49z/Q==", "mode": 420, "size": 323}, "build/npm/types-ts3.8/coreHandlers/util/shouldSampleForBufferEvent.d.ts": {"checkedAt": 1752045724651, "integrity": "sha512-uRGkefdUTYAzfJFkIlrQXLAb8mTswOfX9ekcVDwRTkMLJTecOBN38uXpdiz6PuKnMhy0atZDRFVEy18EWXqLAw==", "mode": 420, "size": 460}, "build/npm/types/coreHandlers/util/shouldSampleForBufferEvent.d.ts": {"checkedAt": 1752045724651, "integrity": "sha512-xBbKvVpl6ANPxu292qzIja1ktnl8M2xN/9r9OzCVx//YQ6mMm7CP27uwe2YIyuDBPTS34PntqZLk7hvK0v9Bkw==", "mode": 420, "size": 460}, "build/npm/types-ts3.8/util/throttle.d.ts": {"checkedAt": 1752045724651, "integrity": "sha512-vfRGyHKVP2K+G6Rjt/yCHgSSDYz+seUV42cKWC5JkXaYYPKQw7qqgB2hEhF3/dnaspnTLhtd/NE989wNiYIqdg==", "mode": 420, "size": 703}, "build/npm/types/util/throttle.d.ts": {"checkedAt": 1752045724651, "integrity": "sha512-0isEQqvbAda9VTV2XCbiQIDyeFdZCAjyvO+gStwNCP6jcdx49ZxRV7NvEG/vGyqwk8DmnRkwG7PNhgnK9SmVFg==", "mode": 420, "size": 690}, "build/npm/types-ts3.8/util/timestamp.d.ts": {"checkedAt": 1752045724651, "integrity": "sha512-+MqgWZ2r2V3zsVljTQAsDMPz7Va6PKPGxGx+ghQA1zD/5/benEibR3UDStkhJN0m6DRA+s2SLZrSke+IUj3oQg==", "mode": 420, "size": 327}, "build/npm/types/util/timestamp.d.ts": {"checkedAt": 1752045724652, "integrity": "sha512-hYRuaQexNSbExhpd4uAaKVxsimj2fyV097TTjjO8eTM/n4wTs6mY3oCVAHwyOXo17fnY6t6WxKGwcGg329OCcQ==", "mode": 420, "size": 317}, "build/npm/types-ts3.8/eventBuffer/WorkerHandler.d.ts": {"checkedAt": 1752045724652, "integrity": "sha512-Jb1LjAXxZ0pKrzWgFCeMPJ60ebyHDc4jsYlnJiGX5SXtac0GBTJwT9EAZtz37YMNax69f2PF0JzctBiQt3D1qg==", "mode": 420, "size": 886}, "build/npm/types/eventBuffer/WorkerHandler.d.ts": {"checkedAt": 1752045724652, "integrity": "sha512-uXQ3epeXvbq9dTq0IjNBZLvRFIb5mUF9zWMUQSU8cFC9lKAedTBI0KYqtQPra5t3SKAbObusIf9B2dbetLY5Fw==", "mode": 420, "size": 863}, "build/npm/types-ts3.8/coreHandlers/util/xhrUtils.d.ts": {"checkedAt": 1752045724652, "integrity": "sha512-v66IyDA1otyBQxrtik3RIa+RYd+qLyi2xloFEz8zid4275r4UqFG0kDI5NGtPv8N3TIV6rnJsIlSUvGUKDd34g==", "mode": 420, "size": 1241}, "build/npm/types/coreHandlers/util/xhrUtils.d.ts": {"checkedAt": 1752045724653, "integrity": "sha512-2hYGq3ajyiIQuI8WNTr+eVON4lcfnHQVXc+dBpCmVKnFNsLC4m0kKTde+SMG1RKkGoaN5ai8dC5+fIPzhK+9iA==", "mode": 420, "size": 1205}}}