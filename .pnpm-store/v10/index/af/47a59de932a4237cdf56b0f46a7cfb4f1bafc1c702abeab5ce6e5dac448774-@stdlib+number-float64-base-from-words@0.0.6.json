{"name": "@stdlib/number-float64-base-from-words", "version": "0.0.6", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1752045720966, "integrity": "sha512-giZYqp2QbwsOHNySg9FuTpE8Yyro5HyjpXkP9Ck5aqbhCvG1sKLbnlYjh5mR2+1rIt/vv9FN8ffyrszTfwbkMg==", "mode": 420, "size": 23963}, "NOTICE": {"checkedAt": 1752045720966, "integrity": "sha512-4cuPRRYzgfVMbZIC790aV9oIoAEVC+4xyLwcB73N6IHP34DxsSH2gT61hzrQQsrrPKyZ3lwkqLKxnRVCIg2vCw==", "mode": 420, "size": 44}, "src/addon.c": {"checkedAt": 1752045720968, "integrity": "sha512-0fgzx/pKgbfMGZ3ZpS4Dy8nWuaYhbitEoee6Ic2YxFRzPZUfEMHIYqSrhrfg9efiITXJjBYv/NmHsNYESmxLiA==", "mode": 420, "size": 2976}, "src/from_words.c": {"checkedAt": 1752045720969, "integrity": "sha512-+vn+jP8N2lOLyJWeGJvkuVrqfCNFydy2q7dEe7Lx61st7saDjq9auggGpdeZ/sqBcVxMAO7DIV1fui8WDag1OA==", "mode": 420, "size": 1330}, "include.gypi": {"checkedAt": 1752045720842, "integrity": "sha512-0fx2dSIpi4EJSLlzXTmRgX2VCgClRuuP7Yy2gYGkxOYPC1bukM/nrdHEkhKPLr8jFzko8Ga9sX4HSfHlw6M7tg==", "mode": 420, "size": 2193}, "include/stdlib/number/float64/base/from_words.h": {"checkedAt": 1752045720969, "integrity": "sha512-BYDxa9MLhvSsWleQ+BPEfGuDCh4TG5PGyhzCSveSNIz2sHSStmAb9GpU4TVVtAX25zAw/TOJQKJtMq+f025NgQ==", "mode": 420, "size": 1205}, "lib/index.js": {"checkedAt": 1752045720969, "integrity": "sha512-erG8RZqOw5s6HFuUqvpzzrXBxNLiKizmND17V43g8VrhH7qZqmSb4o10PuL1S/B51d19LS7X9uHqyMfL3Z0Xng==", "mode": 420, "size": 1443}, "lib/indices.js": {"checkedAt": 1752045720970, "integrity": "sha512-kCIxTFw40sm0KufOBqHk1TI3YV48iKH3t/Gm/8kPiHJPWBN2D/rRo54UI/fOjJYVbwRtMI5FmawgU+u22+nt2Q==", "mode": 420, "size": 995}, "lib/main.js": {"checkedAt": 1752045720970, "integrity": "sha512-ykb0iJckf6hmhQ/F5cY1Zg3nBqoCNfYk9fSqsnYEtMhTupL+lWWVpMmtuejXZS2Ki8IRSGW9RNasCO3rzAaHNQ==", "mode": 420, "size": 3157}, "lib/native.js": {"checkedAt": 1752045720970, "integrity": "sha512-cx7A8FMZRQFBh5K2esJTaiVe6EJpfAqoKz+onWtt+SMc55ddCjgMP5BS/3MMtlBDAYLkWR/J/B7gkgfZd7F7lQ==", "mode": 420, "size": 1692}, "manifest.json": {"checkedAt": 1752045720973, "integrity": "sha512-DL3VInV9eezzGpy9+6FqPCUb98I7pScyRbxMNSkC5q06SXHMDY/l17sM/6AJ6soGKjGrHdp1DoiQxZk8CKcoUQ==", "mode": 420, "size": 536}, "package.json": {"checkedAt": 1752045720974, "integrity": "sha512-vm3K12T41b+RbpyDESbZseQhg5/ojOn6E75SvVHuH2jCJu+vtVYgR0XOtRN/+6wOGuIqTVMWCv/J+bG3JwQEYg==", "mode": 420, "size": 2568}, "README.md": {"checkedAt": 1752045720974, "integrity": "sha512-1GNyJpajP5kGrKdTl9RshWDcCZvW+Tw55BEtaxcpzhXNxTxj/vWwzXOcJa59yOw10ZshTXtB7p1w4zYRHpZaFw==", "mode": 420, "size": 7231}, "docs/types/index.d.ts": {"checkedAt": 1752045720974, "integrity": "sha512-N9xNvQuJv8VGNLl9sb2HI8f3MrZST0n0yI5BU8yn8hOH+RK9IdsOq7gDOkTAss10yEPaCuia+ec4+XWwH3rOwA==", "mode": 420, "size": 2726}, "docs/types/test.ts": {"checkedAt": 1752045720981, "integrity": "sha512-RAt0c5yjgD+T1AM1IWpXHzkpJidFvTj6zrk4FqH9aePHM6HoPMxRxQ7b97TT56mh+m2rLkJhZFuqGWsCgEQHVw==", "mode": 420, "size": 1720}, "docs/repl.txt": {"checkedAt": 1752045720982, "integrity": "sha512-jW8moWochg78pHvZ7q2zxxWpU2PxIcjBSUidoi+d8Xli/VkJjeAd9FTjl2t/x9989p2Qe96YWq29ov4RRrvTKQ==", "mode": 420, "size": 858}}}