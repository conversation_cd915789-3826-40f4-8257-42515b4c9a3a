{"name": "@humanwhocodes/config-array", "version": "0.11.14", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1752045736892, "integrity": "sha512-3Gto0TuM+VlkS5NfEZKwLHGqelz2U71DtEgPqJ7sjU0/FqInjsjDtAqx/bIzsxc6eP2DWQ1vc54Mno/1bCglVw==", "mode": 420, "size": 11357}, "api.js": {"checkedAt": 1752045736892, "integrity": "sha512-k2A/j5W35SIfn7fPZF6LGSts/L6eD48gq+jYUkS6gooud6OjESlto73i1/+qMquoRX5EqKAeEr8ncyfsahTQrA==", "mode": 420, "size": 28425}, "package.json": {"checkedAt": 1752045736892, "integrity": "sha512-ZV1Dti7bNglloi7JllAnBcxBlXdKlwAgojV1mo2vZ6ykLUHVKRhLDaKuaacMmf2JTuZyaaAM8qrj08GfxK70bQ==", "mode": 420, "size": 1496}, "README.md": {"checkedAt": 1752045736893, "integrity": "sha512-NaA3MKudIC8yj5wNiodu3bU7N6anFTWjpjLydTz9/x6TmUx1N8vclyj0afDUeUe5NJkqXt/xXamJg5K6CwBVMw==", "mode": 420, "size": 14365}}}