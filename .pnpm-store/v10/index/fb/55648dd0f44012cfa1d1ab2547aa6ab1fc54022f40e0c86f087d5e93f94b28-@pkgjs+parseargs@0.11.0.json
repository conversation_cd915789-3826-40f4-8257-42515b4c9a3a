{"name": "@pkgjs/parseargs", "version": "0.11.0", "requiresBuild": false, "files": {".editorconfig": {"checkedAt": 1752045715512, "integrity": "sha512-nQjb8pJWQDKNIfCTjtmo8XBOqStufi62kQcOFiEe5GT75gx6MZcOUYpNvn9JCC41SKnCd83n1Or3X1cvob/YFA==", "mode": 420, "size": 299}, "LICENSE": {"checkedAt": 1752045715512, "integrity": "sha512-3Gto0TuM+VlkS5NfEZKwLHGqelz2U71DtEgPqJ7sjU0/FqInjsjDtAqx/bIzsxc6eP2DWQ1vc54Mno/1bCglVw==", "mode": 420, "size": 11357}, "internal/errors.js": {"checkedAt": 1752045715513, "integrity": "sha512-O8HXi33VcS44+FE4Zm5jGHFCPEpey2xQiKvuglTTbfjpalGFI0SKUZOd976+anIyaXE816XqS79m03TDfTPr0A==", "mode": 420, "size": 1431}, "index.js": {"checkedAt": 1752045715513, "integrity": "sha512-9k8aqAjnyCzf4feF7QeLva/+NDRfsGmvm1jr/LOOQiAf6i9BIdQEh3+tLO0EM6B6kazbZcr67x8yY+MUIIQ6hA==", "mode": 420, "size": 12936}, "examples/is-default-value.js": {"checkedAt": 1752045715513, "integrity": "sha512-Xa5Sd+LYeJ9sPZapwuOdHal68I1yGoTttocNTFVB0m4EFkc/hre3hYYJRHalP4e0azo2Qg8YkYTOu8CtZSmHgA==", "mode": 420, "size": 765}, "examples/limit-long-syntax.js": {"checkedAt": 1752045715513, "integrity": "sha512-+B7d5haCcz0C8+VwJ5TrjzabJQvIZ71G62GvGEfb3EGPb332zj3M5KJ7kF2OBoJHoL5I1J2HM9K9CslsTmMbuA==", "mode": 420, "size": 1071}, "examples/negate.js": {"checkedAt": 1752045715532, "integrity": "sha512-dt1RGEVQyA8izcGdSzGV3XhpDyjil9fzk8a4ju6m9uPpMnXXf1ZffHCRCV/JGuzWqmtmoNwl33gBD59UYFhKSw==", "mode": 420, "size": 1314}, "examples/no-repeated-options.js": {"checkedAt": 1752045715535, "integrity": "sha512-9oLq7+lm4q2kwnfLWVRe0EqriPbocJJOb44uspQ8EaiqZx1OaC0TPHnBPk0KUCGCpGlv4BhkJvggIA2h35tn5g==", "mode": 420, "size": 897}, "internal/primordials.js": {"checkedAt": 1752045715536, "integrity": "sha512-WR7ckxFCz5NlNJsRxNqzPZmXGCzdBKmKN6FFtHd+r3sug2PzIdG7A7hUrES2KOxf+Iz93mGNXFXBoUUM9iMoBA==", "mode": 420, "size": 11947}, "examples/simple-hard-coded.js": {"checkedAt": 1752045715536, "integrity": "sha512-HoRnCrYQyQ6YdNqdP/7dgG8ZsCXrFtUIw71vfkO1e/Azzu4gf9Z0HpzT6m7558y+tjlxxb4l4OLzfpiKQ2mRMg==", "mode": 420, "size": 540}, "internal/util.js": {"checkedAt": 1752045715536, "integrity": "sha512-Ilng0mGyCTi07ZGz5oMvYC6Z70Vf2MCLww/mE6c+IIEnXy9IXT1F1sg2vOE/YdhTRENaoFnt5TVmL2dXWDFYYA==", "mode": 420, "size": 235}, "utils.js": {"checkedAt": 1752045715554, "integrity": "sha512-k+1C8Z+GVYWdofKfmSc+ZMP/yCMmDPwE9VJ5/ycilvIrcJjwqSvtVgzx0IyMZUApEIWfACqmJDberFAEcqqUew==", "mode": 420, "size": 6251}, "internal/validators.js": {"checkedAt": 1752045715555, "integrity": "sha512-qnyp2eEpsVzjW3YAOgRsu/NQS/Om1g77LRJqq5Q2U16arK2k8qW1Vi5/pgv0mjX1bj34hzqdxbkKVJOTOVl6cw==", "mode": 420, "size": 2243}, "package.json": {"checkedAt": 1752045715555, "integrity": "sha512-cL3V5SoVObVJyndEDF3JBaqlNmVvyFdvSxvPUPqVxOD+9deUsYVPbfviHow1iGsnehEvOx87ZsOk0tFpySSdnA==", "mode": 420, "size": 881}, "CHANGELOG.md": {"checkedAt": 1752045715555, "integrity": "sha512-UrxSbECRMCW24x4Tm7bmGgj1FqeQQIs1pNIJax15WW7lIJw65th5BepjbbMC9+QB7jqJcKFvHX72WXa9CfLw4Q==", "mode": 420, "size": 6968}, "README.md": {"checkedAt": 1752045715556, "integrity": "sha512-3ti3XddEe9sxUQu3U0elNOlHS2vyxL+rPWmKlaLkGM3aSNfuaXR3oJYtSEqj/QLNcZZqkWZKW7rduZ79LPQTHw==", "mode": 420, "size": 13642}, "examples/ordered-options.mjs": {"checkedAt": 1752045715556, "integrity": "sha512-vT4xaaFCtdY36JCxE4XIEm9vctIKbctUCD2qp6ueJTD6iB5UfguDysGqsu893N7/bpIRF4SWseJMbMYpRAbcNA==", "mode": 420, "size": 1396}}}