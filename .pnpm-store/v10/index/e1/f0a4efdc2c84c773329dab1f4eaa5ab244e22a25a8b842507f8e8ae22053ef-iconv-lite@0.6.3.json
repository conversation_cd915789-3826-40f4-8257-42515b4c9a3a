{"name": "iconv-lite", "version": "0.6.3", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1752045735469, "integrity": "sha512-tQC5OulIvgAjKXzNmIc7S4yKc3mDJrhl640BgS+MIlUb9E6t/uwq3hfUqpS7+6SptFbZc3vHpW7W0fkTxSYT7A==", "mode": 420, "size": 1064}, ".idea/iconv-lite.iml": {"checkedAt": 1752045735489, "integrity": "sha512-p0oma0aDmZDIdLECrQaBI0J/g3L2njQkUMBmx+x+6IKRmi8ORnyCTtapDsBHtPDytZom4Yra2SVgQrTSFaV7Ig==", "mode": 420, "size": 458}, "lib/bom-handling.js": {"checkedAt": 1752045735490, "integrity": "sha512-vWLfBAavL2fUX8QY+uxw1tY/WBEnBM/fmii8YzaWBDVUoJBI5pq7tOPd5lPYX51k6dDVwBF8fZ1ASRTaUyP8Bw==", "mode": 420, "size": 1109}, "encodings/dbcs-codec.js": {"checkedAt": 1752045735490, "integrity": "sha512-mI5O9lr/vZZmwq9BOIx7mH1pRZswNgriqbdS65fXVm8vdE4G5IDUhrjBNjIZiyvQo+0dHCnlHkeavhhgXy8T9Q==", "mode": 420, "size": 23065}, "encodings/dbcs-data.js": {"checkedAt": 1752045735490, "integrity": "sha512-41WcweY4Bw7PLJkUiRdAsZAaIZG9gMQwT5iekvj58yY16Muy+CpSg1Dd5E8AQ5unRLomOyLPW2pspTp+bss9UQ==", "mode": 420, "size": 9389}, "encodings/index.js": {"checkedAt": 1752045735490, "integrity": "sha512-jAlnxUUDYxrmCS/ttrxMj5Pe27IIVZPcHt97pT6xbcPcGxY3WAC62ZIttgx1mIZWjI2g2J5ccC95UybQiV+U/w==", "mode": 420, "size": 733}, "lib/index.js": {"checkedAt": 1752045735494, "integrity": "sha512-CvrW7nWH/q+767zUQQ94wbiIwNIrMGiyUHYcrWGS6Z3cKOeOMowrBdo6I7WsOE3Dp211cD1jMlNjdX7noBggjQ==", "mode": 420, "size": 6321}, "encodings/internal.js": {"checkedAt": 1752045735495, "integrity": "sha512-B0RPI9wGtnlTWg2GvfL9AVl5ogueSoR9hzZI4gQbgPYkCbYC+NpHxnLxC6oRNpytyW2v+pGx5bil9usB9GqpLA==", "mode": 420, "size": 6309}, "encodings/sbcs-codec.js": {"checkedAt": 1752045735496, "integrity": "sha512-goVnWr1i9xdNLZTBUEezAy5hbnPohfgHW3ygc6WTIsOR69aMoxRLvriwbnAmYA75w73kNzPyXrfWiQFn0PkHpQ==", "mode": 420, "size": 2191}, "encodings/sbcs-data-generated.js": {"checkedAt": 1752045735496, "integrity": "sha512-q5FUhH0pE21KXnx7BhBXBObPQpZvJgZth/jlTrXOSYSXV0AKnoTgnY8MIv+kX07fRzOVJfjl3Ab9Qn3IWlo0Fg==", "mode": 420, "size": 32034}, "encodings/sbcs-data.js": {"checkedAt": 1752045735496, "integrity": "sha512-bK8xe8+FhuASBY+ZexfXxTMJyPUg7cCKAVEL4n+Vl5LjsMgR2Tjtqr3Eg4h1GO+X8/iyaFCbVQ0VykLo9Mo2QQ==", "mode": 420, "size": 5116}, "lib/streams.js": {"checkedAt": 1752045735496, "integrity": "sha512-Jr5efn1GmQhONOlg2vrD26rTpxuNKFKc1ZB/qCvgWLoIX1cTZJbB+WqjDvPeWsOHmBdR6AajVEb3CTlu12n+NQ==", "mode": 420, "size": 3380}, "encodings/utf16.js": {"checkedAt": 1752045735505, "integrity": "sha512-NUl2KIIUUr6XKZVb1IcMZiZ5nJTtytYNb2OBQeJT4JRzGdwETjT4QFmuz3IxydplViua2EvOb+aNhQQnkCW9og==", "mode": 420, "size": 5502}, "encodings/utf32.js": {"checkedAt": 1752045735510, "integrity": "sha512-3S2hk5gu8piFbmK49gmPivyh0z2DpG9If2VLxnsHlfdZQd/4pQUC5TQkTMcLH0U5yh1Pn2DeEyoDxceyCkD8RQ==", "mode": 420, "size": 9982}, "encodings/utf7.js": {"checkedAt": 1752045735511, "integrity": "sha512-qp9Ku/x0QdkwB4wwcz7wGhf815y7DoDE8PBPqRTnDYEyJ5EuuiEAmj5m//+/ZxPgZuNUrfw5IRen9JISJMWq2Q==", "mode": 420, "size": 9283}, "encodings/tables/big5-added.json": {"checkedAt": 1752045735511, "integrity": "sha512-rIPvbAmvklgnO1hrCzNhS22mdZkwojvL8dDlQor8B2dnkWvEmwSpihv5Urjo+csPgSg9dHpySvARYtuhhAeb5A==", "mode": 420, "size": 17717}, "encodings/tables/cp936.json": {"checkedAt": 1752045735511, "integrity": "sha512-eBZnCByCKB9pM02RfFPtgafSg8C3zNXDk1kXkKdGycAJYmVAPyhVUVc0DGnjejQLzuI8PieqC9mnID1fyB/edA==", "mode": 420, "size": 47320}, "encodings/tables/cp949.json": {"checkedAt": 1752045735511, "integrity": "sha512-MI5KEFEWOqM13kolVonFeUiMsKAe/8TPVzksD0Kbv8rUZvaYYU67+uOHgr1t7Qkd6rASGWYFygI+JrahxFyQDA==", "mode": 420, "size": 38122}, "encodings/tables/cp950.json": {"checkedAt": 1752045735512, "integrity": "sha512-YAh3DY6Pzg8YscXyvDe1GQjsx1/FDWKapQVxP/XoqTT1+VcXA4It8NItO2d3tBkrcnAF52zOtXgTWkG0bC/eYg==", "mode": 420, "size": 42356}, "encodings/tables/eucjp.json": {"checkedAt": 1752045735512, "integrity": "sha512-GpI1c7LUHuerQr9s0hvRfQS48N/i66dBkACydou1aTyO1aMhcan044KYE2jFgshAnNMqThBnpNTUdjXBJ6BCwA==", "mode": 420, "size": 41064}, "encodings/tables/gb18030-ranges.json": {"checkedAt": 1752045735512, "integrity": "sha512-tXpFTXHk8B1JrcYGpcarVhaGnFTww/nDC4Dn7xOmckbp36Wqygbpawp2AEzPyhgBdYJRQbsqc2bmXo9+RDpD2g==", "mode": 420, "size": 2216}, "encodings/tables/gbk-added.json": {"checkedAt": 1752045735513, "integrity": "sha512-8DnBOdTTx+Dc8E66Mf2mcluGjSzUF2S7oAcn5FjivDub/RyINGXrUl3SlCoMEVawVOD6sEIGFKENjY+UuJ/Dcg==", "mode": 420, "size": 1247}, "package.json": {"checkedAt": 1752045735513, "integrity": "sha512-p6GYWd/jaSiWn2vvmUPMaKwIM0pKgj3ldUq6qtxmRrH/ciwf+2pW5HUW/+s23PMDBZRRLouQ4qjKLtMvjZny2Q==", "mode": 420, "size": 1131}, "encodings/tables/shiftjis.json": {"checkedAt": 1752045735532, "integrity": "sha512-4CRJ01YaTThWyYH8w1DQg2uj5VyC3ZZYXgVfbhbc/SwdPs5Ez0c/VZJDAaEUP2bdNyeT7dE7owJUspRGV6OEHA==", "mode": 420, "size": 23782}, "Changelog.md": {"checkedAt": 1752045735533, "integrity": "sha512-PLByQRy6T9V5vh2BzMtUAqxdwpkXbrR4+Fx5oGy6OpITZvrjJhbsK3ow08/FaRN5xkS/H8GGD1sro5l03i1RLg==", "mode": 420, "size": 6581}, "README.md": {"checkedAt": 1752045735533, "integrity": "sha512-+56SZ9ltMQm/DTRt44SJ4iHXHnkgQxxN9tNFXM4LKk95tCht+XgJ+KQM0PN7LVM1rBOOwLRS51O0McVeZ/svuw==", "mode": 420, "size": 6352}, "lib/index.d.ts": {"checkedAt": 1752045735533, "integrity": "sha512-d/ZzVn1iDJyDmWfo5r7GzE7pdkXjw2mPsYmXNyrQBu3TPrvHUksFrZ1tJmsqfMjwINVv2X85n4hKvhggIuHoOA==", "mode": 420, "size": 1387}, ".idea/codeStyles/codeStyleConfig.xml": {"checkedAt": 1752045735533, "integrity": "sha512-YGtR3gHSXk5kBrFx2RJ2or1K0kyNadAZCDJAzv+rsP5nPZ0wdmdV59OIbu3jYZgYowfCPfC4AmXT9LFfbqf/xQ==", "mode": 420, "size": 142}, ".idea/modules.xml": {"checkedAt": 1752045735546, "integrity": "sha512-q7IPkGJB/doVsd+75zDksQcTphQmC4gL9BD34iodbtoQuq2F5JGq0fR1cClxgl67Mj6cflcWxNzT6OaJGnqQ9g==", "mode": 420, "size": 272}, ".idea/inspectionProfiles/Project_Default.xml": {"checkedAt": 1752045735546, "integrity": "sha512-rgOUNqq4kapWBpXliA6E2hOsz7x6aBm0h5pEai5fb5AqRyweVOSzJapH3iKczWtt91/CBsBMriQeyYvWhBH0Tg==", "mode": 420, "size": 251}, ".idea/codeStyles/Project.xml": {"checkedAt": 1752045735546, "integrity": "sha512-kY/TO7qmAwChzFRXTCqDorJPSTfFUXg4YbIxtGna0fSLyTIEKlZvW5g5zDsvNtx6Kn+uPfBS46WuQz4/2t37gg==", "mode": 420, "size": 2141}, ".idea/vcs.xml": {"checkedAt": 1752045735547, "integrity": "sha512-Brly6bl2urXIc1b9q+fyBU2UehHEZEFc37fi6KJFbKlTTE8eX8ufAWvWCneYxxhGsEMc0U3vNuFIVI+VpvZTJQ==", "mode": 420, "size": 180}, ".github/dependabot.yml": {"checkedAt": 1752045735547, "integrity": "sha512-d/IqlFjj+5znf3jOMBJdyduYXrMfk2RvWK/6ctNqhZ1wba3dziEx/CoGF/jTBduA5BxLbZxBWhm6SxxutPVAeA==", "mode": 420, "size": 321}}}