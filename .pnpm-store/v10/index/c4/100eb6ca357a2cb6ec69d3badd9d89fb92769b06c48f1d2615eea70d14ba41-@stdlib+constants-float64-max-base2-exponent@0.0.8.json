{"name": "@stdlib/constants-float64-max-base2-exponent", "version": "0.0.8", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1752045719965, "integrity": "sha512-J7jAJS6uUMo84Cq3xWcGZMDIJOA+s9oQifPwoA0j5kipVry59TZFxteWdKh8TMhtEIXcM1kRvgIQ1pEzaxIYVw==", "mode": 420, "size": 10174}, "NOTICE": {"checkedAt": 1752045719966, "integrity": "sha512-4cuPRRYzgfVMbZIC790aV9oIoAEVC+4xyLwcB73N6IHP34DxsSH2gT61hzrQQsrrPKyZ3lwkqLKxnRVCIg2vCw==", "mode": 420, "size": 44}, "include/stdlib/constants/float64/max_base2_exponent.h": {"checkedAt": 1752045719967, "integrity": "sha512-2UZK6ZsFcMwRFGjNTROY3cnq0xQtI19JTmVp53hdKJLEjZNrDvTY63YqS5vgnh4nbervYr5niEhLs5qTe9mDPw==", "mode": 420, "size": 941}, "lib/index.js": {"checkedAt": 1752045719968, "integrity": "sha512-AYAhxDI6UNma8TMrH/FhxkMBxYAiC7He96gKZt7jnY0JcNJ/4Ar4cD3fQb3CzyB8ba35bg97S9i2WkG0yZLosg==", "mode": 420, "size": 1353}, "manifest.json": {"checkedAt": 1752045719970, "integrity": "sha512-0GXmtOUquUPeI1Ts2GeVv30m3SBcS90QSryHb4PwtcQBnvyNDf4uU+DfSiozU17w+rYWbqRS/n21SUDCVtQ4xw==", "mode": 420, "size": 543}, "package.json": {"checkedAt": 1752045719970, "integrity": "sha512-jf8DOpJMg8ZqLgXov21QmRpAkIjC90fZLekT4A0vyzbqc7ALc0nHNNwFTKL/6kFbLwvEXg18wC3pZtbvQzvcgA==", "mode": 420, "size": 1774}, "README.md": {"checkedAt": 1752045719970, "integrity": "sha512-xBJhPt8SZ959mwsBebmnaypYlcqhIUh6W7jcPjNUzH11zMyiXKrn4Zk+7OI+mXUmkezgsQvk18d1dgf1ULOFOg==", "mode": 420, "size": 7047}, "docs/types/index.d.ts": {"checkedAt": 1752045719970, "integrity": "sha512-g6WRS4MyNPjtUulZJ6pxOmozspN1gSaxePFxQRFP9XpvdUpFsWv1FkgaOqldQbgOwGy5jHnKxiWR0Dc1h+YGAg==", "mode": 420, "size": 910}, "docs/types/test.ts": {"checkedAt": 1752045719970, "integrity": "sha512-3k4RypPLvngGX2Igbs52/ZkWgy7eZiIYnPXt4tSU+iNxtWu+yRvbHbkP4poyRr0MXFJqQVlPWcLlCRBSGI6NXA==", "mode": 420, "size": 824}, "docs/repl.txt": {"checkedAt": 1752045719971, "integrity": "sha512-8p/vGMHjr85VOWEStw317YKMo5j4lhOsJPgsZx1XtBeR5sfAGCwzUUpWx9gI9hrUAwoU8nMtxKfO0sIRIZjEEA==", "mode": 420, "size": 180}}}