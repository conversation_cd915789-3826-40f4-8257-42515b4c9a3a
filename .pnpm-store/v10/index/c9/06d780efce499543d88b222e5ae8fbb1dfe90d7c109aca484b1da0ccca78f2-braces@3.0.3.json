{"name": "braces", "version": "3.0.3", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1752045713671, "integrity": "sha512-cjkrzNiWTIjsiqPYFXRqK2pEZtnHyo9CjX0PPiuxFnTvSUyjNciyVe7lglwIene7RaXWACXzGLeKZOGb7M0jxw==", "mode": 420, "size": 1091}, "lib/compile.js": {"checkedAt": 1752045713671, "integrity": "sha512-DiY+a85sXV/HyyIu9HvkqYhGgk1dfMOW7fomF3I4G1ac7BzUk2Zma/9ZPUunhlXpm5a6sxw+JP6VIgAcYlrkYw==", "mode": 420, "size": 1501}, "lib/constants.js": {"checkedAt": 1752045713672, "integrity": "sha512-NGgbbPKdGMl708QWvkTkA/6zcUDla0LXbNmcQ17VFJ+OyD3xLLFsZrXwj5DvBnJ1Qc4uXPoZFNw/3mYYgEM6PQ==", "mode": 420, "size": 1589}, "lib/expand.js": {"checkedAt": 1752045713672, "integrity": "sha512-6xzP4Zgbg5bw6h+hBT/cezQKRx5a9FFdnHRRc4TnvukJmBJUWmvtXNXBuk3xQXfhwxqj2UHQl7Vyts3Cosl7Og==", "mode": 420, "size": 2797}, "index.js": {"checkedAt": 1752045713672, "integrity": "sha512-3pSTzuACPheQSmaXXwEPd0qco74qu1+0vkjRWMhiZGFYHMDoqwxCp4GCWoNllLvyYjpcMGoA/YtO39n3uY0cEw==", "mode": 420, "size": 4380}, "lib/parse.js": {"checkedAt": 1752045713672, "integrity": "sha512-J2gKmeZxS0WEigYVh9GSTrnQ6j4aMzBJZyHRPL9sfGd0Sfa8oH2apY2Q1VxJsyjq53N5SjN17H3Ca2YZAIO0oA==", "mode": 420, "size": 6899}, "lib/stringify.js": {"checkedAt": 1752045713673, "integrity": "sha512-DcoGxtqZv+cN4ipaUeeSYM23arkdrhDkQW0fKMCvdDS1xWu5Urar47o4mmVLaaG6oz/cv2wU0rChGvbF+iCpsw==", "mode": 420, "size": 708}, "lib/utils.js": {"checkedAt": 1752045713681, "integrity": "sha512-Cz6YUZBhxpcj7i2Qf9NCM9pJXhjOJAuPEifyHkjjPiN4ixjbZS/aE+khXnoHG6Ca2gY/hSGZ0h5cudkITAMgXw==", "mode": 420, "size": 2518}, "package.json": {"checkedAt": 1752045713682, "integrity": "sha512-1wzR00BYoEdw6UX2sudqpLoh+j83L6quDddAos5w0WlJF8y7fZXQUe3mkvbnWlS8eKWAtiugAQat5UnlZjAg1g==", "mode": 420, "size": 1647}, "README.md": {"checkedAt": 1752045713692, "integrity": "sha512-+XF6NjDx8LROM61K17wrP1pzFWjmWKAAzajYfmMQ/+yUquSxzVFY6NbS7GXfPhOS0AJUB8lOEUfBYVmgHUJSDg==", "mode": 420, "size": 21505}}}