{"name": "@stdlib/array-uint32", "version": "0.0.6", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1752045717051, "integrity": "sha512-giZYqp2QbwsOHNySg9FuTpE8Yyro5HyjpXkP9Ck5aqbhCvG1sKLbnlYjh5mR2+1rIt/vv9FN8ffyrszTfwbkMg==", "mode": 420, "size": 23963}, "NOTICE": {"checkedAt": 1752045721603, "integrity": "sha512-4cuPRRYzgfVMbZIC790aV9oIoAEVC+4xyLwcB73N6IHP34DxsSH2gT61hzrQQsrrPKyZ3lwkqLKxnRVCIg2vCw==", "mode": 420, "size": 44}, "lib/index.js": {"checkedAt": 1752045721603, "integrity": "sha512-l61ZViQJHVfQ7GMqEbW/QcIOAZ/JHcjlJfCGxDjaR6zh7SWda2F1OaNqbqAFbMm5VT9vn6ZvgyQT46vW2PvxzA==", "mode": 420, "size": 1251}, "lib/polyfill.js": {"checkedAt": 1752045721604, "integrity": "sha512-AI616n8WdK1CAhYqApjdWBUH/RVB5bPxa3XuN3dZ5MXghDGq2jXoL2oDpbjVZX1OK6w4ITl2Sm+bF16BHyqmFA==", "mode": 420, "size": 916}, "lib/uint32array.js": {"checkedAt": 1752045721604, "integrity": "sha512-ME0Vtypmqp9oli9nh461vqkM+WDZRhlJGijFf3nEwxjD+oQz55tNECkUAazEDbyPJtfcvG3HV+EbNFU9qB7bHA==", "mode": 420, "size": 804}, "package.json": {"checkedAt": 1752045721604, "integrity": "sha512-K40X0tRY/19NNoi5vtoHfoWvn/+BxtyAzs/avbwkOt7G2EZ7wdqlfsXq9Dhkz+aU06/CdJ/hxxeXsp8xwUY4ZQ==", "mode": 420, "size": 2273}, "README.md": {"checkedAt": 1752045721605, "integrity": "sha512-bgPuyWm7uZL8hg2OX+R7GhNXI/kDA8/kGoauegXf8GNVVumHoTNcLNrCA07DTj44SrxNa6LjCEetP/SVeq5Gyg==", "mode": 420, "size": 35650}, "docs/types/index.d.ts": {"checkedAt": 1752045721605, "integrity": "sha512-fYxC2dvt6OWD2aGCSgIbGXEGaBvcR77teJM+SUZjhy8j388c6A3q6WNNCYdDSExe9KFTlNnc1kavs4To0aIpBg==", "mode": 420, "size": 821}, "docs/types/test.ts": {"checkedAt": 1752045721605, "integrity": "sha512-DJhMn5EUtTKiG5A72BH2vTd6zGSPxvIq4bHRc9Ch7yHjwEVnH27EZyjLreG3ukXFirKuaeGX08FT2i4ZPYMXOg==", "mode": 420, "size": 1065}, "docs/repl.txt": {"checkedAt": 1752045721606, "integrity": "sha512-OqHf00flezkZQcILC0ty9UKH6A8NHA+GluNfucD/6l2odjqcTlb9Riy528REdKITVCewgOETaG7EjuOXAhw8zw==", "mode": 420, "size": 22591}}}