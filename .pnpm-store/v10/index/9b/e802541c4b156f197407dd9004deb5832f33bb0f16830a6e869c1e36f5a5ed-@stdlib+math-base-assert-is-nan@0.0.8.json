{"name": "@stdlib/math-base-assert-is-nan", "version": "0.0.8", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1752045720807, "integrity": "sha512-giZYqp2QbwsOHNySg9FuTpE8Yyro5HyjpXkP9Ck5aqbhCvG1sKLbnlYjh5mR2+1rIt/vv9FN8ffyrszTfwbkMg==", "mode": 420, "size": 23963}, "NOTICE": {"checkedAt": 1752045720807, "integrity": "sha512-4cuPRRYzgfVMbZIC790aV9oIoAEVC+4xyLwcB73N6IHP34DxsSH2gT61hzrQQsrrPKyZ3lwkqLKxnRVCIg2vCw==", "mode": 420, "size": 44}, "src/is_nan.c": {"checkedAt": 1752045720808, "integrity": "sha512-hh70uWY1fTIJsePoBEeRZmTmEsbBlFicWIN4hNnRsgYZ8NtOz1qJE+gLJLlE0VSSlIcfb3qPsEAXosYvY8s7jg==", "mode": 420, "size": 915}, "src/addon.cpp": {"checkedAt": 1752045720808, "integrity": "sha512-Q3xCBNMGCkKLb9wgWwkWBZ94bJH/Way6vWV0SRoeBVdH6kW3NFlQosxSTaGYXC+dB5tC/tuU0DiHKChcwYjjtQ==", "mode": 420, "size": 2165}, "include.gypi": {"checkedAt": 1752045720808, "integrity": "sha512-Uu1cNz7f1lD73EfVqHEUD8ZM1M37moBHaJQiJe8fTVaN5CSf6pKyEv2Yw/++cJcF61WQfwipGMuOWho56KEq7Q==", "mode": 420, "size": 2195}, "include/stdlib/math/base/assert/is_nan.h": {"checkedAt": 1752045720808, "integrity": "sha512-Rg7JDOgKhDQPf/d5shxENpKLHmifrcmhR3FnFDxX7p7x1HrLeX7bMBf49PpqkTudk1dFYIrqi8CxZY0qMniFqQ==", "mode": 420, "size": 1161}, "lib/index.js": {"checkedAt": 1752045720809, "integrity": "sha512-7EwKct4MgP/X+Q2ACly+pOGVR1mcIglSIwT4IFrg+8hKZxvRad3QmUIPvXUevfv3xuIkrz4jjIiX//nnU7gCrg==", "mode": 420, "size": 1009}, "lib/main.js": {"checkedAt": 1752045720809, "integrity": "sha512-/jnchh3Dbk1ceTcprQQN4zWKJ7S//NyurmreZVB0Gyxwi8gsNM4DTYbIjtshcASuZn83Aa75WdRMZ5Db8KrCFg==", "mode": 420, "size": 1029}, "lib/native.js": {"checkedAt": 1752045720809, "integrity": "sha512-eah8Y5Glt4v6QjR0BWvrmLEw3cW0UoIneBSQNlu6nJy/WucEA7+A/1iN6brNpsTDpq+0Cd3bBAwXEiPtLs6TFw==", "mode": 420, "size": 1165}, "manifest.json": {"checkedAt": 1752045720809, "integrity": "sha512-A12lQmNEAupAv979uCxQLvF1Jn37rVen5eXI8XMXP+HG0M4pn41Iowp2xBC903WK/4LEUqYfD4Stvbx63eX+YA==", "mode": 420, "size": 485}, "package.json": {"checkedAt": 1752045720810, "integrity": "sha512-XdhpiVJqYXh+LV8t38y9LesSzbrKM106CAA2d0qqu14i5nRwGdxm5fTSl/rCh2NeXFoAPEOcQeHC0tHUFCT8vw==", "mode": 420, "size": 2114}, "README.md": {"checkedAt": 1752045720810, "integrity": "sha512-Fx2qMiA1a5FTcnrZLVL7Xl2h2XKwiIiuBz9RtW5CxUkuOn8nXwomu7QJgcfbLB5Zkenwsf7XnhA8PdyiGQs5RA==", "mode": 420, "size": 4557}, "docs/types/index.d.ts": {"checkedAt": 1752045720810, "integrity": "sha512-dv5qtOg1oXVkxg+llb7+TfiQQn9UQNZRkJCAtzHzXFD/l0f26Yj4ZyjflqVL2D7Nd47WK0EhqCiSlSrwiYW2sg==", "mode": 420, "size": 1003}, "docs/types/test.ts": {"checkedAt": 1752045720811, "integrity": "sha512-8GlE9UutDF7SeytOPDxhIHeiSheK/kqjXlbMCxUJbSzsRbDLjvZFekUeQb/m1hX4EGTHPpeoN3VCC09wxC9UNg==", "mode": 420, "size": 1320}, "docs/repl.txt": {"checkedAt": 1752045720811, "integrity": "sha512-/ZYZ0vEjXM2YFDXnKMlsnRvXVpFzX9z1dqgG/HB6IWknmpv2VTAwW+rzSuuPCKIHtuBkMxBeLkNn6z/Q8KoUZw==", "mode": 420, "size": 392}}}