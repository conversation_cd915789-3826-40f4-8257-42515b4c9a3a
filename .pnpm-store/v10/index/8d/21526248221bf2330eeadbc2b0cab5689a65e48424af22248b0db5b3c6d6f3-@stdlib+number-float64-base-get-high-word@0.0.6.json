{"name": "@stdlib/number-float64-base-get-high-word", "version": "0.0.6", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1752045721574, "integrity": "sha512-giZYqp2QbwsOHNySg9FuTpE8Yyro5HyjpXkP9Ck5aqbhCvG1sKLbnlYjh5mR2+1rIt/vv9FN8ffyrszTfwbkMg==", "mode": 420, "size": 23963}, "NOTICE": {"checkedAt": 1752045721574, "integrity": "sha512-4cuPRRYzgfVMbZIC790aV9oIoAEVC+4xyLwcB73N6IHP34DxsSH2gT61hzrQQsrrPKyZ3lwkqLKxnRVCIg2vCw==", "mode": 420, "size": 44}, "src/addon.c": {"checkedAt": 1752045721574, "integrity": "sha512-365GIjaN5L/Ff2B0PpZ/W8Oc2DYp0RuaO1c9WKda9nh/qdT/XYH7ClMysC3A9oPSZnnCpvPqJdoJV+eNpJe1UA==", "mode": 420, "size": 2598}, "src/get_high_word.c": {"checkedAt": 1752045721574, "integrity": "sha512-UZoYRiXKK9+UOBTVc5tOEJsD/LOD6nnyQ9My+PqTqvQ5MTALyEs18WgPx78ERSM2Mn6OJOozw2WIihqaBYblNg==", "mode": 420, "size": 1211}, "include.gypi": {"checkedAt": 1752045721575, "integrity": "sha512-0fx2dSIpi4EJSLlzXTmRgX2VCgClRuuP7Yy2gYGkxOYPC1bukM/nrdHEkhKPLr8jFzko8Ga9sX4HSfHlw6M7tg==", "mode": 420, "size": 2193}, "include/stdlib/number/float64/base/get_high_word.h": {"checkedAt": 1752045721575, "integrity": "sha512-32YKRhYUif9xCKpP58UhZI0eVcDSeD2hcsVFUnw+s8Il3OaocjRUiWBNzqAEkgCsfZrq0KyyFK9ooqAOt6gWng==", "mode": 420, "size": 1228}, "lib/high.js": {"checkedAt": 1752045721575, "integrity": "sha512-UlxXnqOqUEny510OAQ9aXKGquDoDrmjYNCm1e16yfFxDCX4Cj6ZvMbjL6wwGfJ/Dxdn2fuisDj8hdomaaIim5A==", "mode": 420, "size": 876}, "lib/index.js": {"checkedAt": 1752045721575, "integrity": "sha512-mZpdAjL+V382dAhJgCB5R3of/SaP2zyF5BsXv/aSRrVwo/tuULsvFqLGgzUMFqCGelTU1pyKvo3UxFEj1lWTug==", "mode": 420, "size": 1115}, "lib/main.js": {"checkedAt": 1752045721576, "integrity": "sha512-A+98h8xOL+JkVkZOBejqnZ7lzoX+ZWtimQ9ilPaAsaeGzCwJ5HLLAAW+z+5nG2I3tEomF18QEriqP7FTNZFNWQ==", "mode": 420, "size": 2522}, "lib/native.js": {"checkedAt": 1752045721576, "integrity": "sha512-6DjfovyInYsX5hIb8tB/6P+p5tP359zuUBfAd0U49iWrzKoXS3HocMSrb9OtE/Yiw+5VIOS2S3iWUW7SSeWHwQ==", "mode": 420, "size": 1140}, "manifest.json": {"checkedAt": 1752045721576, "integrity": "sha512-wPpUpOzSQE+ckoKg7zg8Uqkx58+AZs17CzzWwzxWtkHsReqIeRym94pIwaDguhBkAaQJ+TbcNKj868/r3l9X1g==", "mode": 420, "size": 539}, "package.json": {"checkedAt": 1752045721577, "integrity": "sha512-It+CRXrPyFoAvU2oYVDF0BkLfkE4nLjkAhi+XXwfICYhvT3y4HndEWHwRtnaDvrgM07nZLxscqxnuRweta3nhQ==", "mode": 420, "size": 2726}, "README.md": {"checkedAt": 1752045721577, "integrity": "sha512-8n1pHYUKvHk5ospjyehfJxCV/RIIlKlpuBacUO3pTC7XpUKanDkezI5JQkXpRgHrRitcDdNt0qUoIZTU5YOQsA==", "mode": 420, "size": 6527}, "docs/types/index.d.ts": {"checkedAt": 1752045721578, "integrity": "sha512-lGZxUd8yY+LqsdiJiodTWrVYLVxKEkaYQ8d/VgkiUn7BWEzzwGCS9LODduNnrBni1l0UWQt44PszWsqyl04Sig==", "mode": 420, "size": 1063}, "docs/types/test.ts": {"checkedAt": 1752045721578, "integrity": "sha512-LpnN22pC7HhsAzAFPnCZZzu0TRfKaLdXYyefqNuXa1X5ke088o2OOmDtGwUUJyNl5lbNG67+m1EmOW7Nyqm9NQ==", "mode": 420, "size": 1235}, "docs/repl.txt": {"checkedAt": 1752045721578, "integrity": "sha512-0bv5q9v8jXTi++cWZDb4uiRo/rhgfP7gXvtlS8i9tXkFBfpxCSchfieYL5rWN77jytcRdUBmEfAzyd3yQUr5AQ==", "mode": 420, "size": 417}}}