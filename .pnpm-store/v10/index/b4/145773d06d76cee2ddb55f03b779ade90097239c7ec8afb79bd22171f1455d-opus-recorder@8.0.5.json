{"name": "opus-recorder", "version": "8.0.5", "requiresBuild": false, "files": {"dist/decoderWorker.min.js": {"checkedAt": 1752045730790, "integrity": "sha512-zMeBSLnnrP44M/YmP8fSA2Dq+dwJ7g6UFgL+rSSht+CJx/EtZrya0UI17JGY0eaVuJj3pJFHTGR/PJkj5/baWw==", "mode": 420, "size": 19966}, "dist/encoderWorker.min.js": {"checkedAt": 1752045730807, "integrity": "sha512-p1mFQi5mOMjKc5K7TLnSCnq5ZqO1k3Phkf9m5hFI8fT+WCqhpNFRFw+UsoqcmSMVHlzHlIYKGRFftUml4tbb5w==", "mode": 420, "size": 385096}, "dist/recorder.min.js": {"checkedAt": 1752045730807, "integrity": "sha512-83I0cNDDOAPu8RFyQC7TpQugOT/WU4xIKoxllvP9+T11HpDDm6Dl9fkDM7z4AwX/uCCM6SX1+yDmRHUWjUnjQw==", "mode": 420, "size": 8046}, "dist/waveWorker.min.js": {"checkedAt": 1752045730807, "integrity": "sha512-kKU4/frJbcqjtj3CCvjktDp00y9d2YXiFS71UissG0PcGa40AcnH5mEoqHo+9JaBSH0y8soxcWIlqwjUIVWyvA==", "mode": 420, "size": 6345}, "package.json": {"checkedAt": 1752045730807, "integrity": "sha512-eCIJBEOVWqwhZ0G7g4ea2Rv10xjRV1wUfoJCegc0wo/z+Wn+q6zCuPjE49QONlVy3kChXyTZB0f0BLwODHc7eQ==", "mode": 420, "size": 1152}, "LICENSE.md": {"checkedAt": 1752045730808, "integrity": "sha512-GjdF5DlbyswB8Z3JTZ5fKdcFqEPuCd/yDXnkZ41czlKofdKp+0wazdZeA4ESZuUDh8LgJM5lOKYg97bhB5URvQ==", "mode": 420, "size": 4609}, "README.md": {"checkedAt": 1752045730808, "integrity": "sha512-DRMbIe/d6DrnM+t0xQxpuVzi+Q3urc6ySqXAE6xzkAb5jNDYqP1C/rFFtlBRNX/FDV2vbicn8o13SUdC/FZX5g==", "mode": 420, "size": 9477}, "dist/decoderWorker.min.wasm": {"checkedAt": 1752045730809, "integrity": "sha512-LFpCqTDHpv72gL/AsHn1o+xat5pKyOYy9k6/E9YXZ+3WUuNoyOQgjd00hDM/Gdlld2jQVixue9HzMqHcBoHjbw==", "mode": 493, "size": 149534}}}