{"name": "@tanstack/vue-table", "version": "8.20.5", "requiresBuild": false, "files": {"build/umd/index.development.js": {"checkedAt": 1752045724867, "integrity": "sha512-urm1xsqT1xFp/0MbSxohfHTbPEaEaPw9ZGSGhIKcHNYhVPI6ah1ssLEB5CbLonGiE7yUFGMuXpdFstmITdrYHQ==", "mode": 420, "size": 151358}, "build/lib/index.esm.js": {"checkedAt": 1752045724868, "integrity": "sha512-f2coTFa6/qLIrK7lOsj+QUVvos6qsbZ0a27KgvgRFPPfwpfjdhcNjvZZTm3MOQ1HsAeB+y2I8NABQ4XKQjll2g==", "mode": 420, "size": 4308}, "build/lib/index.js": {"checkedAt": 1752045724868, "integrity": "sha512-NpbRddej+0xK/IV/emndSb3u87mhE5h5KlVkIY/CeODbcBztyQCAvxYD/m2miejSSWer1bU8Vw4i3MDSqhU/+A==", "mode": 420, "size": 3115}, "build/umd/index.production.js": {"checkedAt": 1752045724869, "integrity": "sha512-m40y2PYgSk3CW6B566JEcccZp4WWjDg+QL3Y9APbzYTRZf0wm2NQELahw2RfBpobQwG7qC2Avw8Tq35VZFd6Mg==", "mode": 420, "size": 59533}, "build/lib/merge-proxy.js": {"checkedAt": 1752045724869, "integrity": "sha512-QSDyUsyzmv7DI8aPYDjr7wd9yUoUdUG71Ga5KUsEpFnYMx+A+WKUngqaZA1EjgLB+xuAWBnWVu40bAi7Xs/eHg==", "mode": 420, "size": 1814}, "package.json": {"checkedAt": 1752045724884, "integrity": "sha512-KsDHqqaxS8ZHRm+C3ER63n7SEvHlZP+adt9jnU8+X4Lfzyy6ee7p1Kg1LemBSuZ1TnENlw4MlMzNma9Uwka/4Q==", "mode": 420, "size": 1171}, "build/umd/index.development.js.map": {"checkedAt": 1752045724889, "integrity": "sha512-02UqoQEj1aHsJx+NbR0eBnyOmq1wvB/bIi5HJ7YBSePgufHFEGMQytJbh1RuX4JfzFyMU1g99ET89DOmAFDQOw==", "mode": 420, "size": 299862}, "build/lib/index.esm.js.map": {"checkedAt": 1752045724889, "integrity": "sha512-In9VJIyK4DVFjBjTk3mCiHhasy2S9C7zBvnXuLbgeQK/8CGtpFllU7U4iuzSGzwxcpAcx5XZ9KS6vQPtoyC4hw==", "mode": 420, "size": 10956}, "build/lib/index.js.map": {"checkedAt": 1752045724890, "integrity": "sha512-N9/2l8SL9uTZlWkEkaAlyCDErihdKztWkqYhJeRpLA3vI/9HofT1uCUtgynKr6+htbd/KLJjio1tyKsAv1gqog==", "mode": 420, "size": 6261}, "build/lib/index.mjs.map": {"checkedAt": 1752045724890, "integrity": "sha512-IoGr+Q9/8ZebCT5HjUrymS3/yVGFHXve5Cu2e8MzCM+2jb2nsJuxscd8qcyBjy0OJjxmj+eWw+zIC5VEDLDi+w==", "mode": 420, "size": 10953}, "build/umd/index.production.js.map": {"checkedAt": 1752045724916, "integrity": "sha512-bRfataCyQIkH+c9ZMb5gyaSxdHb2I8wQgUHa2756f/Qk13j5wzbhHYVcIKlLPTDd+cegHDrtVR0iEh98xjZFrw==", "mode": 420, "size": 236333}, "build/lib/merge-proxy.js.map": {"checkedAt": 1752045724916, "integrity": "sha512-tYoFjRmfYuVj1llhmAswE+Ls5uR6G/Df1zQnh6QHCcHo5tzvp727DYP78MdN/XhvTtlei0H+T2qnHhbt/Qx9yg==", "mode": 420, "size": 4832}, "build/lib/index.mjs": {"checkedAt": 1752045724916, "integrity": "sha512-mz0qcmDuoq4eMLK9b06ePUPAP+URXoEa4EZxiqEdk45lxi8gvno0/dCU4lsKYYfee8TYwt7bcVbe5Tb6XBfGJQ==", "mode": 420, "size": 4305}, "build/lib/index.d.ts": {"checkedAt": 1752045724917, "integrity": "sha512-Ycr8XELEsUa6RM+1J956sYPKrGQGBaQ5jVjFsTuYZgm3yiPpj86OiOwESz9B3q2NEAs/Zdr/KCTF1U0tfP4/Aw==", "mode": 420, "size": 835}, "src/index.ts": {"checkedAt": 1752045724923, "integrity": "sha512-8549c0MKAl5nV5bssm2MCaNfpjIwJAyvgCd1SvR8tcZnHHY9+kiBw70A01IiTpGgMTNq4uqm1XpQ+tULtEiiyw==", "mode": 420, "size": 3165}, "build/lib/merge-proxy.d.ts": {"checkedAt": 1752045724943, "integrity": "sha512-mPhUAPUMjzbUNCO+ODnwfdsh4SBbZ4We3PszHhmexVgDCMIsmKzPVIlCdr2itrfB8NVMzwq+l171uehtyckUOA==", "mode": 420, "size": 541}, "src/merge-proxy.ts": {"checkedAt": 1752045724943, "integrity": "sha512-nO8Cq5uv4oyoHMMzJIUtSF3OHwUBaxIoU9ehudFBjdQJftiivvGboH/Bs3CHsG8alNoM42i787iGM0ZlRsrCGg==", "mode": 420, "size": 2154}, "LICENSE": {"checkedAt": 1752045724167, "integrity": "sha512-6loa2sUZkuPZ7vY5MIl7aEhsUUepiT+8fefOQhQw8Wgit14MqGj1QYoQOewBpqfWqveXT2iQ4BeKHffOiiNsFQ==", "mode": 420, "size": 1071}}}