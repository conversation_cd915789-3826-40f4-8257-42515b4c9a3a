{"name": "@stdlib/assert-is-plain-object", "version": "0.0.7", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1752045718536, "integrity": "sha512-giZYqp2QbwsOHNySg9FuTpE8Yyro5HyjpXkP9Ck5aqbhCvG1sKLbnlYjh5mR2+1rIt/vv9FN8ffyrszTfwbkMg==", "mode": 420, "size": 23963}, "NOTICE": {"checkedAt": 1752045718536, "integrity": "sha512-4cuPRRYzgfVMbZIC790aV9oIoAEVC+4xyLwcB73N6IHP34DxsSH2gT61hzrQQsrrPKyZ3lwkqLKxnRVCIg2vCw==", "mode": 420, "size": 44}, "lib/index.js": {"checkedAt": 1752045718537, "integrity": "sha512-3gVBD3lO+/YZF7FQ3UiWlGlxnkdZiM7DdqJAjbQgchdhK69TrJgKIi8go4HLX17LWH2Sv5ejum1ahq+KJPL2Tw==", "mode": 420, "size": 1016}, "lib/main.js": {"checkedAt": 1752045718537, "integrity": "sha512-XF5R8I+F8lxolqm+iT4y1tK40x51mrySvjkxUH1e8GfxV/HDGIdeKy1OxBhEE9kZTKnf1fZ5En/5IZaOQ57nsw==", "mode": 420, "size": 3057}, "package.json": {"checkedAt": 1752045718553, "integrity": "sha512-k26hmssOyE7q2UNpWe3RNAHRxtK85Ovbr1g7bXSMoJNDSqv6vIMcWh22xEdfSoUHXs3WCyBbgm1DOK8yWjOePA==", "mode": 420, "size": 2070}, "README.md": {"checkedAt": 1752045718554, "integrity": "sha512-p8tIHhq66fuMVDLihAzRFO77vZBIlxtajmR5/Ky6zpoEDxBSq3fb2LFcsXR6iFtasxXHkBpDu+hQi/ZFCF3TfA==", "mode": 420, "size": 4635}, "docs/types/index.d.ts": {"checkedAt": 1752045718554, "integrity": "sha512-lh4HHeZcn/3AhrMY36pPqDqGQcB+NKCOhSXoWljYqq16Es5qTXdAOSHgUTlQj5vbGEj1qkW+Qo3t0d3Gz3q4gg==", "mode": 420, "size": 1014}, "docs/types/test.ts": {"checkedAt": 1752045718571, "integrity": "sha512-NCM+oXPDK6cEMH3j5IqKpb4kDyYJG4qAoMxRfDfSE5fJjfa7e8EphIbEYwWFO2e+ikfqEssCyRvcyqqrARysMw==", "mode": 420, "size": 988}, "docs/repl.txt": {"checkedAt": 1752045718571, "integrity": "sha512-4xuGTjROjjZMMUjiX7KxtislrqIcBztxmrbspaKwsKhKXHNUWhrYrS3EJ0SL5tISOV5gNaQ3aZtEgv4L1B2hVw==", "mode": 420, "size": 370}}}