{"name": "math-intrinsics", "version": "1.1.0", "requiresBuild": false, "files": {".eslintrc": {"checkedAt": 1752045729357, "integrity": "sha512-t0HG7zl7ysZGGdyGAe8bb7bb/dmxg48xXNU8FVVTU4+8eh4bxCmXXkeLVswnOn0sRL9Lqw8WTcvFo/BJNmuxiw==", "mode": 420, "size": 229}, "LICENSE": {"checkedAt": 1752045729358, "integrity": "sha512-PV8GlePH5+YfCyV+THSYfrdEzoJRVmCPetP3EARzKwI3Bnh0i0nE3ydoRviqvddcBYQkV5ERF5qIyG2qZg8FrQ==", "mode": 420, "size": 1073}, "abs.js": {"checkedAt": 1752045729358, "integrity": "sha512-7i4X/3+4MOfyDigqD3DY49vReEta7hbndsaha230pl/wSN6gQsdyocPmftL5Irpnnuwxt5+wVJjci4Kn4oFuIQ==", "mode": 420, "size": 73}, "floor.js": {"checkedAt": 1752045729362, "integrity": "sha512-4564yspDuOUzgQqf8NDYlGO+rXho+Y09B4OjphdJI3NPLwRW2JP9h39ixeVyGGHdvkhGKSNax2i8XrnPx1PAug==", "mode": 420, "size": 77}, "test/index.js": {"checkedAt": 1752045729362, "integrity": "sha512-cFGzVCzvl/yi0sr9VIGu9W6pbQsdfV+29gkzqeojtHJvvZpCsmdOYj1STdbMWFkx0ECyZj76H2YkNHjdgzyj4g==", "mode": 420, "size": 6320}, "isFinite.js": {"checkedAt": 1752045729363, "integrity": "sha512-ZD65yfmuGLAcKFjnzyOJ3UBMdi3lVMz66HCwv9rMqOPW+YyBQ2jj3cBHQlEdPWvL1j+Wgwi7X4ZyyKaOaOqoxg==", "mode": 420, "size": 262}, "isInteger.js": {"checkedAt": 1752045729363, "integrity": "sha512-vTCFvOhYurU7qKf9Qmp5K2KseIjK/xf/9T66YzYIui8XXw/OsVcfyxkDN6/KAeG93RhFKfb+p1U+iElW+1z4jA==", "mode": 420, "size": 410}, "isNaN.js": {"checkedAt": 1752045729376, "integrity": "sha512-fN7GU6tDd8SEhHpK053ef30+zjsVvCNR2EE+o0G/cIWJXjELUGk8/M4ygw9XDIT5yFElf44D+9/zde4t+k7kcA==", "mode": 420, "size": 121}, "isNegativeZero.js": {"checkedAt": 1752045729376, "integrity": "sha512-ZCHASm/10sSxbXBWVo3LK6uQab574ky4l3tRjyJVfKG5SLjZyVpR1bJNh5YAnOHDG5VA14dafIYWwBfmUd8jVQ==", "mode": 420, "size": 143}, "max.js": {"checkedAt": 1752045729376, "integrity": "sha512-TKB9qiXV/X83fxZACD1UrCeRuDVQwkWL1xh6BK5kb3j0zOEb1zamY0ZZH4HbqWarNflkgP8ZKh6AP5JqqNKpDA==", "mode": 420, "size": 73}, "constants/maxArrayLength.js": {"checkedAt": 1752045729376, "integrity": "sha512-7+mf60rnlWerxFF6pNZxV0hqMHVQq/jgE/FMVrw26NwwyKpuDx63y8Y924OMtwF/vEos/M9pvHuCMQ90MD8hVA==", "mode": 420, "size": 110}, "constants/maxSafeInteger.js": {"checkedAt": 1752045729376, "integrity": "sha512-PmUmybfQ4qyOqovVnzH4sEZRYFOhV3d5uaICpWOXA1N/I6efbtunuSneDFjU1ZLC69sJBkJtXiPrdKaLtoVR1g==", "mode": 420, "size": 231}, "constants/maxValue.js": {"checkedAt": 1752045729381, "integrity": "sha512-UfVlskqjNoQlld02tMLAbHVKCRrPPWp81hWjGvxiP/7c0ZbeOxQH7tvogtEwIfrujUe443IwuG80rug7RMbp+A==", "mode": 420, "size": 197}, "min.js": {"checkedAt": 1752045729382, "integrity": "sha512-4I/tL3TnheL++XMI7TYly/L95J+zpxi5ZLR4C4awCAHcLTNK8JLaJrWabCGWFz5+UGZZ8EaOLhry8SGcfHFHLg==", "mode": 420, "size": 73}, "mod.js": {"checkedAt": 1752045729382, "integrity": "sha512-1oEmt81ZTZ0fuVhPzQeXk57MxiC9oz5/8uronmlzlVC8OMFg3VGZB8HKqpPKlWFx9ejPlFNl2fhIyHlSU6u+pw==", "mode": 420, "size": 218}, "pow.js": {"checkedAt": 1752045729385, "integrity": "sha512-X99R8yfxGhEwx9eN6p1PriWy7sAxACR2/h3DpkGG+XGsWboHD/+32LE6M4b39TRSKEzXSyntzyCfaa5vrfi9KA==", "mode": 420, "size": 73}, "round.js": {"checkedAt": 1752045729386, "integrity": "sha512-xdQq6GrJ512Wuc/EAv99hDn8KoS5waeCacLp9ya0bWXv0p9pmswpELLRkV8oSiU+YZLikvHnaJQHSoRs4LIEFg==", "mode": 420, "size": 77}, "sign.js": {"checkedAt": 1752045729386, "integrity": "sha512-ss2Wuy/P2ajFUcFa+pD4FprS2lPHBmiWhCx7eza+pq4/YxL7qHAgPrxbBtkgbLUceM2vuusaL39iCOBsc8fq2w==", "mode": 420, "size": 214}, "package.json": {"checkedAt": 1752045729388, "integrity": "sha512-vdSe3cE+0CX9zxu7avCLBCFwksEihhZXrYrl0i7dZdnGcf+DHa4y6cdsr7jfosboEodB5HBROjjKwjOynAmdUw==", "mode": 420, "size": 2670}, "tsconfig.json": {"checkedAt": 1752045729389, "integrity": "sha512-0s306a9Llz0UU47lwXaGcDfYAmVA/6Kp/nGPZopEiyfWGcUNeNSbdqyx0A8P0Kpzex1bH5YWGXBpzlIdQD1/1Q==", "mode": 420, "size": 36}, "CHANGELOG.md": {"checkedAt": 1752045729389, "integrity": "sha512-HlsIESxvpHNx/CXn1imsGzs4CPWR2rSz3S6rDPOm3A1A/Ps3voA5fT160kDQDZMyw85kZlGyXTpQh/iTWuFV+w==", "mode": 420, "size": 1466}, "README.md": {"checkedAt": 1752045729389, "integrity": "sha512-trCnal3OUbE5jWs3+JHrH5vuju0SY47nlcQT1bw/uvzgZVv+FGDw2cXfiV9PxngrwahSOyW3ECkQ5n/SI9hunA==", "mode": 420, "size": 1884}, "abs.d.ts": {"checkedAt": 1752045729393, "integrity": "sha512-qMYnYdhChjqgqNqU5yiliPcpyKLmHnOqEUtHllsx2eQINU25udmLVgCc3P7O9H6/slp5hTk/e2D1zDvvhuOGdA==", "mode": 420, "size": 18}, "floor.d.ts": {"checkedAt": 1752045729394, "integrity": "sha512-ZyFUhJVyjBuUnsG/A99jgvnU5+uDg2xQIQVNKewbHyX/il85mNONSTtlM0atL/wgozheDbN0Ioj/gktPkD8QOw==", "mode": 420, "size": 20}, "isFinite.d.ts": {"checkedAt": 1752045729394, "integrity": "sha512-ql+eGS/EbBAyG85DKEHzCmQBSskmohD7UjJgJUBkBJbm4/VYiZexOVH6OLbTqOIGWEOpl5ly4f3sT5zeXHKicw==", "mode": 420, "size": 80}, "isInteger.d.ts": {"checkedAt": 1752045729394, "integrity": "sha512-n0XHB0fl2Tn2xrLPZv1EekLsHWaENfaa+14KhlO4/9VU4r9cCKvAaKP9NHrbEiylZL0Mz7yvCsRc+AItY05dsA==", "mode": 420, "size": 87}, "isNaN.d.ts": {"checkedAt": 1752045729394, "integrity": "sha512-+hbxfCsoKNzUitvQY5zp7gorezoI2aDs97idLMkdSBCI087kxn4hgMLUkk8T2j+aiWjuv7V1TzE/aj68TWjwaA==", "mode": 420, "size": 22}, "isNegativeZero.d.ts": {"checkedAt": 1752045729402, "integrity": "sha512-LoVwzZ1DBFyGIa+AYdrCV5iNgcNzYwBKPiaCGTAVe4c6RDLo7z6P6oR6ZO1XALBhy/HLQUUMXXxwPxechCxLaw==", "mode": 420, "size": 79}, "max.d.ts": {"checkedAt": 1752045729403, "integrity": "sha512-//V+9su2+8li0y8nVqQ2vr6JyV4WZ8M8qf47Gl6WAlr+TtJJJs1eIBT82U/IQ2Se9yqs/1gdJzo0bP+H+DCKTw==", "mode": 420, "size": 18}, "constants/maxArrayLength.d.ts": {"checkedAt": 1752045729403, "integrity": "sha512-B+i3z6u/300BOhxvUf978ffNb36IpB+HsVa8baA4U24nPH/j8vay+KkoA6TG3hZCP0HKPdoBl40NN7xL9Ejd7Q==", "mode": 420, "size": 71}, "constants/maxSafeInteger.d.ts": {"checkedAt": 1752045729405, "integrity": "sha512-RdX8N92OiP13MO8g2Y0AjGucJKekvIsSfyc1Dm1U6+f2z8mzChjtc2Cnv5LaLjGfJv+VoWE0iKcV9dwdImFgpw==", "mode": 420, "size": 77}, "constants/maxValue.d.ts": {"checkedAt": 1752045729405, "integrity": "sha512-b4SoJWnY+qFKkQGvL9wp3dQh8kvyfkrw7Jh3+JROfTKhyjCPlj5QwoWYXlPfO9B9foSfXpw8tFO6Dswr2Rw+7w==", "mode": 420, "size": 71}, "min.d.ts": {"checkedAt": 1752045729406, "integrity": "sha512-+0cBPSUpGfWK2QkJ1KyFIVYJ2yavi1pmG2c8u0OH7OucPPfNacfALtyNUAFJh3B2Neq+jHS9ysbdNKMXPsETdQ==", "mode": 420, "size": 18}, "mod.d.ts": {"checkedAt": 1752045729406, "integrity": "sha512-7Syh6w/cv9g9yXcvLQN+Dr84pPdttoDkjXZjsQ0R6NLPoJx/geryrzhwf6I/KC6UAzFE2KhOvk0kS0MZHKNosw==", "mode": 420, "size": 76}, "pow.d.ts": {"checkedAt": 1752045729406, "integrity": "sha512-hfI506JAToB6m2NS48Syd4d2MRdusOGh0qzBKjtHa37XSWVolGPSm9rVP8xU8WnR5EFkTSiiFoM9I+H11CCrDQ==", "mode": 420, "size": 18}, "round.d.ts": {"checkedAt": 1752045729406, "integrity": "sha512-twZTPfK38cf1zZCFDzf/UDwLbo/AldLsh50FeayEyZZOZl9hvBofW3gTM9mwclc+gzARaLEE7jJY9UCiUiN8hA==", "mode": 420, "size": 20}, "sign.d.ts": {"checkedAt": 1752045729406, "integrity": "sha512-qggNcbDn8bzLqlidCsw0A/o7gr1BRek0jEhAC2hwq650+yKRjIGOURHR+1gkgDCTCEofOsqn6VAchqDUbdJMIg==", "mode": 420, "size": 57}, ".github/FUNDING.yml": {"checkedAt": 1752045729406, "integrity": "sha512-jdpaxckn3YwyL7e9qJOy3ROPcnHf0FhNXWB1aZP+ESHdr0oVdggqt8MU0xftPq1nP5M+YBedksyBrrIB5sCNFg==", "mode": 420, "size": 561}}}