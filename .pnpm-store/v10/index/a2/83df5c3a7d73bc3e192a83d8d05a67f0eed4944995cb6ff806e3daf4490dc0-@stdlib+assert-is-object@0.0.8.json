{"name": "@stdlib/assert-is-object", "version": "0.0.8", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1752045718806, "integrity": "sha512-giZYqp2QbwsOHNySg9FuTpE8Yyro5HyjpXkP9Ck5aqbhCvG1sKLbnlYjh5mR2+1rIt/vv9FN8ffyrszTfwbkMg==", "mode": 420, "size": 23963}, "NOTICE": {"checkedAt": 1752045718807, "integrity": "sha512-4cuPRRYzgfVMbZIC790aV9oIoAEVC+4xyLwcB73N6IHP34DxsSH2gT61hzrQQsrrPKyZ3lwkqLKxnRVCIg2vCw==", "mode": 420, "size": 44}, "lib/index.js": {"checkedAt": 1752045718807, "integrity": "sha512-ieNSbFu8DEi4OZJ/IVRQo476tPLx4Wu/OCUOCKMIcdIjAwv3RA0S4TCUJKstQJQI0VqokY77RxSTe55MVK6CSA==", "mode": 420, "size": 974}, "lib/main.js": {"checkedAt": 1752045718807, "integrity": "sha512-zT7bY1AxgRbw5Eyunc+mC35Sxw7kdP223ycbQOpSdhjsv/3E5klI/RAVFNN+TItMMyrQYA4qaWFls2OezjNEOQ==", "mode": 420, "size": 1153}, "package.json": {"checkedAt": 1752045718807, "integrity": "sha512-UaE3LQk8k7PR2FiGxunjbKIgIWop+ccNczxeX4lApFf7q8I78avZysIsf9Q0si18+EsA+DQQmyBzTky9KOL1gA==", "mode": 420, "size": 1779}, "README.md": {"checkedAt": 1752045718812, "integrity": "sha512-WvvcYFIaR4UgRqTFlkmO6ivjoNsFfL58+KdL5sHzR2uSdmLL2h2Qikaar2+39d4z01PA6OHSfsgYPqYrIWxE0A==", "mode": 420, "size": 5102}, "docs/types/index.d.ts": {"checkedAt": 1752045718812, "integrity": "sha512-WnTak0EOTRgjxIrfD/XhFrllWO97wvCqiLGcH8MyofA/3OOvuuBiwS/wyz4CQ9HIB8/nWxy+LH0hMgpV/3GK6g==", "mode": 420, "size": 996}, "docs/types/test.ts": {"checkedAt": 1752045718812, "integrity": "sha512-s6jLdli1VgdFFWKbzB69wNuPfUxy/mmFf22m653jIHOikHXqRV9DPeexoQeyR/ccm9HgfPf0oEtaiQsPFcbclg==", "mode": 420, "size": 963}, "docs/repl.txt": {"checkedAt": 1752045718813, "integrity": "sha512-Ey8VgJVBIfbS+udVFoBYhlkC+YNQJ/hJpC/vh3/mH0sQDiJS0umt9O4AhLTw1BCMx4FngCEeoQLBJAmuyvPVww==", "mode": 420, "size": 372}}}