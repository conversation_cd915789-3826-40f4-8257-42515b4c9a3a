{"name": "@stdlib/string-base-format-tokenize", "version": "0.0.4", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1752045720188, "integrity": "sha512-J7jAJS6uUMo84Cq3xWcGZMDIJOA+s9oQifPwoA0j5kipVry59TZFxteWdKh8TMhtEIXcM1kRvgIQ1pEzaxIYVw==", "mode": 420, "size": 10174}, "NOTICE": {"checkedAt": 1752045720189, "integrity": "sha512-4cuPRRYzgfVMbZIC790aV9oIoAEVC+4xyLwcB73N6IHP34DxsSH2gT61hzrQQsrrPKyZ3lwkqLKxnRVCIg2vCw==", "mode": 420, "size": 44}, "lib/index.js": {"checkedAt": 1752045720189, "integrity": "sha512-im0SlV+RBCVIWdYuiJrbU90WEGnMgc/bKUF9Fpj0+SoE/M52ZVrtvGr5V4euB9/2T4oYwrYshR4QladhavjFcA==", "mode": 420, "size": 1049}, "lib/main.js": {"checkedAt": 1752045720189, "integrity": "sha512-o1CvzGWmWMmxdW94ojT7WwUYZ8nxfSa+bespddW2LJHdQ1Gy2QeiGDmdZu0Z22gs63LB1MQcNAmBQ9uWsgqn6w==", "mode": 420, "size": 1978}, "package.json": {"checkedAt": 1752045720189, "integrity": "sha512-4d4TtO2c/iEiNq7R0oLiuK/1x4XbGrEJrYVFu/r6484SuY2ugHo7o0KOT+Z5JPyJPWDcF02JHcL0LmCzavzdww==", "mode": 420, "size": 1768}, "README.md": {"checkedAt": 1752045720190, "integrity": "sha512-MoWiJKgNkaa5sQyBD7gTu5YxlcMFx0exXoHJzOq3DpR8MHLQ0Xqi6ySn0IMkAIdWDSEAgWmiEC8GnPYT6SE+ag==", "mode": 420, "size": 5564}, "docs/types/index.d.ts": {"checkedAt": 1752045720190, "integrity": "sha512-pjwLCFNmr5NqyUOagtkDQ/WS3oHK+ZwkZ7HElzS8hHaLni/znwMxJ5VskBSJv/iF4KkYrTlLJb9jId8/G05wUQ==", "mode": 420, "size": 1499}, "docs/types/test.ts": {"checkedAt": 1752045720190, "integrity": "sha512-V8qa6HGv8We4K2GZIa/teww6kBr1g4hOYmCHjzma7drouCCCGuS2g3lPP3nAENvzE815HT44o81OkATsO94Weg==", "mode": 420, "size": 1364}, "docs/repl.txt": {"checkedAt": 1752045720190, "integrity": "sha512-0inNCxMqASqMudlhYoRRUNmKrR+JOh3PPsNCJhtYhxRWeOsEiIVcrcmiVc9Kh3I2Xol1fq7FusmEuv8LtA6rqg==", "mode": 420, "size": 537}}}