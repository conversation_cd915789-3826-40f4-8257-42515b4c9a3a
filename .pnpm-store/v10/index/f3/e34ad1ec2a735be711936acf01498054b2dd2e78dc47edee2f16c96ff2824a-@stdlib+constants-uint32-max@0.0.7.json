{"name": "@stdlib/constants-uint32-max", "version": "0.0.7", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1752045721332, "integrity": "sha512-giZYqp2QbwsOHNySg9FuTpE8Yyro5HyjpXkP9Ck5aqbhCvG1sKLbnlYjh5mR2+1rIt/vv9FN8ffyrszTfwbkMg==", "mode": 420, "size": 23963}, "NOTICE": {"checkedAt": 1752045721333, "integrity": "sha512-4cuPRRYzgfVMbZIC790aV9oIoAEVC+4xyLwcB73N6IHP34DxsSH2gT61hzrQQsrrPKyZ3lwkqLKxnRVCIg2vCw==", "mode": 420, "size": 44}, "lib/index.js": {"checkedAt": 1752045721333, "integrity": "sha512-xhG7poEJoEJ2uZJlUkGowGyvTdCvVlaQZR726/PLrwSDiT5BBUu1zwV8bhoBQoq9kydtIjSh+XlFk2AsWHm2Nw==", "mode": 420, "size": 1199}, "package.json": {"checkedAt": 1752045721333, "integrity": "sha512-JTdHEcQclg6h/QchIjnyyaxmR5d/CGZEWuyTCwsJJNUaWVhfAPizfuiHuzXzNZuC1lg8Z3s7/lMX5NdXyz2L7w==", "mode": 420, "size": 1598}, "README.md": {"checkedAt": 1752045721333, "integrity": "sha512-MJ2AvmoP78zHoiTEx/n65QqxHrKFMVhw4w0p9E1QIanlZB5BqghNVqFTefau3NCAavEQ5S449qHQykCOri7bEw==", "mode": 420, "size": 4524}, "docs/types/index.d.ts": {"checkedAt": 1752045721333, "integrity": "sha512-jJFij4rMp+xfI/aWNF+uD2jZzYjWd4UfQSZbcbOEWimy2S3YGAoRCNkhKPJShyhvCI/j90JoBmWnJqzuXWBySw==", "mode": 420, "size": 820}, "docs/types/test.ts": {"checkedAt": 1752045721334, "integrity": "sha512-vTV/llLMTZv8ak9SqSW4JH+w6bxCkB5koTuImzEcB3QauqZ1u9QgWPvdI1ik05VTh+4iuVjcG+zIb9mXND3XCA==", "mode": 420, "size": 792}, "docs/repl.txt": {"checkedAt": 1752045721335, "integrity": "sha512-axSVfXQrjcATy+XLIxAMP7mi65O3Wb1g8CnVBhDiJu3EKKmTrjj+XWMt2cIJDM/9fORhy/rEQQv1EX833k/5Pw==", "mode": 420, "size": 199}}}