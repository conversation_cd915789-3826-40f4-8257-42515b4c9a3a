{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.10", "requiresBuild": false, "files": {"package.json": {"checkedAt": 1752045735353, "integrity": "sha512-y5kIi4KvSyq82Ir0XIYm9ootdeydI7it3XwrNo8ftwXpUwdZ6pOXqRhmnehhYgY5ILjBHGD58manJI4W8nRI+Q==", "mode": 436, "size": 638}, "CHANGELOG.md": {"checkedAt": 1752045735353, "integrity": "sha512-QXtmq620DrXO1Ub1EaO/we+/5Pj94a79LGrHYqJgDZ57OjHGUab0fWCWLbWkknYozl2EXU/78XBiIzMC1Y1W6Q==", "mode": 438, "size": 3428}, "index.js": {"checkedAt": 1752045735353, "integrity": "sha512-bemenYVzxsw9XivUmBmkqqtGBS4y2iEbqe37b1Ta9TYi8ESiW+Y0vPayjmzYmdZqNUW4TxZsFvrC3v0f3MVz0w==", "mode": 438, "size": 59}, "LICENSE": {"checkedAt": 1752045735354, "integrity": "sha512-J368mqOcs0Cp2lG7J4Gr1iF1jUFWV9w0nQHga+ijxGKKKYVmhm34EKbSovrcgweaOb3CkybW0geUxHHtRTRI1w==", "mode": 436, "size": 1079}, "README.md": {"checkedAt": 1752045735354, "integrity": "sha512-WnAbk0Fpkttwp3Qf+pOqdsknGYSj+z3QwhGgeuT4LXa1HThblMk6K9GiGvcLO0SMUW3AuPzfGjy5Ep3pCQFDhg==", "mode": 436, "size": 8404}, "lib/action_container.js": {"checkedAt": 1752045735354, "integrity": "sha512-4wcHFz/4KcLQeKbsz8BTu7HV5z1+s7J3lpbaIlNl+Bxe5B0TvT7D2c6pZu/IHSZlv4C0h9gsroXg4BrLfRdQQw==", "mode": 436, "size": 15055}, "lib/action.js": {"checkedAt": 1752045735354, "integrity": "sha512-xQgxVSQ7juEHg7HI5FS71Jujke7lKXEXik31AvXZUh8p5S2WzYOnCON+pFoOLjVcR782wPS+tNRToZzTing+6Q==", "mode": 436, "size": 4659}, "lib/action/append.js": {"checkedAt": 1752045735354, "integrity": "sha512-COnnJKTcyRhf14KKqVMqKbzVcQXMmW977Gcx+niWlCF+PREA/jyYmZUOzA+DCalvvTxzkmFSWWJ2DFuRUV7dmA==", "mode": 438, "size": 1565}, "lib/action/append/constant.js": {"checkedAt": 1752045735355, "integrity": "sha512-G2szJNlyvpmj/fyKxpt/zNfQVRVhJ88F9PM4H0DOTbdPsIJvn7+AiQJoCERfVdYR0ElWD5j/gbQanhWdxmsJAg==", "mode": 436, "size": 1430}, "lib/action/count.js": {"checkedAt": 1752045735355, "integrity": "sha512-3VOFM2Aah/m9rdPTRG5kOt4UxXbzOBnR/hC/53WzkJxAR94vGMTzthlswFL35Ih+58SgPwQf1zLRDcjXIfoDNg==", "mode": 436, "size": 1036}, "lib/action/help.js": {"checkedAt": 1752045735355, "integrity": "sha512-D+USyHsG/XVbEB5pZfo0Y4MbJ7NZlh5Rkau4b+fMCoZD4dCSPHaKgROPVhbPY9IEM/b48zpNn0+mqP133PDNeQ==", "mode": 438, "size": 1120}, "lib/action/store.js": {"checkedAt": 1752045735363, "integrity": "sha512-BVKklViQjiAdLc90xUYnp4tQeEliX5fxTRdCZtHuLEeVgmFT26TQ2oc24YHZMPNGRUCwrV9nB53+DAekya3nwg==", "mode": 438, "size": 1350}, "lib/action/store/constant.js": {"checkedAt": 1752045735363, "integrity": "sha512-Pvn0SbvR2Jq9C+GjjFpaT0Jzk47NlH0+IGytmL5j1SK0Gd7yOLMYiPKDmmtrtz4f0qbC2StFdR+fTTpOklgkNg==", "mode": 436, "size": 1332}, "lib/action/store/false.js": {"checkedAt": 1752045735368, "integrity": "sha512-mZBTZaS78wVvNCjS5rEuo0bQ5IyyaERcumcMrQZKvfouaTNJLXJ9SnVvAFMMZu6vGm5RvKtlK/4L1yCyrqsQoQ==", "mode": 436, "size": 704}, "lib/action/store/true.js": {"checkedAt": 1752045735369, "integrity": "sha512-snpvA0rE0WxCiS9RzdiDLGnzkflqfaHh0mnCBN6LeVKzWiwhEBkDWtLEfXXz5XY+hNQiLQFJowPwJabf8VFRiA==", "mode": 436, "size": 693}, "lib/action/subparsers.js": {"checkedAt": 1752045735369, "integrity": "sha512-bL<PERSON><PERSON>0LSUI7PL9eUlyTdfrHQ1Z9q7wWC7aHNV3iD3CGzJsJym7yq/QOdtq2e7hqd7fJFAOcm60UBx2wS48FO4hA==", "mode": 438, "size": 3590}, "lib/action/version.js": {"checkedAt": 1752045735370, "integrity": "sha512-vEGZ1jZtKhURWPSzF04tOsBTPdDPalBb1/Zj7mNmfPFwHSpmSC9XpIXUA+mHkDHsnF2p0UuNbSB1bZ82sSqvwg==", "mode": 438, "size": 1244}, "lib/argparse.js": {"checkedAt": 1752045735373, "integrity": "sha512-/DcjfvkTAxqcMboG/PslCpbmVoHPQzWQwU/H7+zrT0fenjbkETGg8R53mN5dk+PIoiJxzlUCSJrFsem2ITL/Mw==", "mode": 436, "size": 618}, "lib/argument_parser.js": {"checkedAt": 1752045735374, "integrity": "sha512-joW8yqv3NRzJwcPnSxRGvND18akHW3tpa4NXKrdPftH9h1ytU8bsplq7tFyvJZaASNi2ijFFMzo913R8hzE2WA==", "mode": 438, "size": 35223}, "lib/argument/error.js": {"checkedAt": 1752045735374, "integrity": "sha512-j1RR8rKqtg/pIztl03f72BKTeLW23ljE3B00qVxKq7Il+f7i+HbEg1PfmRbsvpkOwK/g9XJHS5qw53ovFaoD1w==", "mode": 436, "size": 1183}, "lib/argument/exclusive.js": {"checkedAt": 1752045735374, "integrity": "sha512-0m3l4u9fIw17eMdtFXthlsMB0HL237AWsqMTM1XDoNHcz6bNSkaNozZqFs8n7krWNxHG0PyAjw8QBLIjggBTTg==", "mode": 436, "size": 1589}, "lib/argument/group.js": {"checkedAt": 1752045735374, "integrity": "sha512-YY/f463ybkDTCvwcRJv2Z3IduXxBKWAMCRetx0g6Q0pydLSL537s7+gAu/tD/UCBC+/VnVEQrxou8W4qgfF24w==", "mode": 436, "size": 2330}, "lib/const.js": {"checkedAt": 1752045735374, "integrity": "sha512-ZMH0NvIUY9/7fjcB1s7QfNZEykWAxGO+FPyTwPD/RcCKGYrThanfSKz4Z3GONetSM0uAd4etcmOmgODQIPZYZQ==", "mode": 438, "size": 340}, "lib/help/added_formatters.js": {"checkedAt": 1752045735375, "integrity": "sha512-3oC1MpSqsCWR1zyj1wD67w+VpTR1i2mFY7Ye8BXLB5KPZUz7rw4EARlmRBgKdPXUIp2sQZFqkto2pPh2Q7YVHw==", "mode": 438, "size": 2647}, "lib/help/formatter.js": {"checkedAt": 1752045735380, "integrity": "sha512-KHmd5QaKo3l2x8rf8fOwFe9f+T9lY3LPcX3BLAlBgBoxC5OFLfhnmzk5uuJ0/a5+1jxttq+cuXOfYgRl+DRmpw==", "mode": 436, "size": 22025}, "lib/namespace.js": {"checkedAt": 1752045735380, "integrity": "sha512-/wvc+AS3N9BFHRgDhsAd1y4g3/0Bblf4PEvQpKemn8k+0heXLxakoZPcsbajOmKyv5x9Q22s0omyQLYbme/cRg==", "mode": 438, "size": 1824}, "lib/utils.js": {"checkedAt": 1752045735380, "integrity": "sha512-6BXIc7qZFmHwwJhX2S5hlQ8lS6FHN2KE/4hcf2Fd0ui35Vd02vIS7Bdi4BkqeBVs0nhwc4CnUsvvi1onsEKI5A==", "mode": 438, "size": 1281}}}