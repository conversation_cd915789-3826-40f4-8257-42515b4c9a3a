{"name": "mdurl", "version": "1.0.1", "requiresBuild": false, "files": {"package.json": {"checkedAt": 1752045730667, "integrity": "sha512-O6qRVzG0xW2cIITw72LowZmEel2iaMvR1Ppig1jHptnyXF7bvxyTpo0meU1XSl8lVqdpMD10s1VwNjeZ5M16pA==", "mode": 436, "size": 323}, "README.md": {"checkedAt": 1752045730667, "integrity": "sha512-8pCvE1SjwVd3IgcfQmbSGq3OpU3wbPksZaHhPOVaLwdjJ+VBVEJV4tIUIPShiF/NzK8KW7+3vMdQa/jW+z4LDg==", "mode": 436, "size": 3439}, "LICENSE": {"checkedAt": 1752045730669, "integrity": "sha512-hIYA0289WZoVpTXV5x/UCCr986Hff1ZkXnUFs6UMqASuXZE0AIvxfArv70E1oSbrUxH2EZb4dVvOkckQ2ovNkw==", "mode": 436, "size": 2303}, "decode.js": {"checkedAt": 1752045730670, "integrity": "sha512-axBr4oY//4igtUmgMV+ei+55dAN5MhIHkLeGap2p1vVyW0SXirLwpw30SpT9ChWrXvKUToIsJNLmkV9Q9EYLwQ==", "mode": 436, "size": 2873}, "encode.js": {"checkedAt": 1752045730670, "integrity": "sha512-QZeLbEDBkpJAdZG5It7z2ps6QBrQVzGfMddmlFU6Gw8NHOYLGQ+GsvFWu+I8sP8JWZEOpg9z4DkojLw9jxLBmg==", "mode": 436, "size": 2270}, "format.js": {"checkedAt": 1752045730670, "integrity": "sha512-IoRdoQh5L+/GQYcTh6yH+2Xd8wsphhV5NBIubc+ibiIKGUggpsGn18aYeAIzjE0394EQcN8n5WzzD19gh6vZhA==", "mode": 436, "size": 514}, "index.js": {"checkedAt": 1752045730670, "integrity": "sha512-<PERSON><PERSON>Kqobs23rL9p05ngbIniTdILMZirjkVegaaH1jx2aSGSsL193gNTlSCyxMvvgXnC2mC83woWIVYQh4iimaLZQ==", "mode": 436, "size": 195}, "parse.js": {"checkedAt": 1752045730670, "integrity": "sha512-v9+FuQPg1MuQaT32QczZ1Fnib7skUfZC1PGkTYfFl74H8gdtgctPBLlV9usJwpskjNQucNxvoJ1Uq3QpTy0L5A==", "mode": 436, "size": 9893}, "CHANGELOG.md": {"checkedAt": 1752045730676, "integrity": "sha512-TpNAEhYuYbQ9p+KVWgaJ/y1neB44t67upDprt3dk1IlLYjUyLEANsrrJxjLA+f0ryYzDfE8smlz+pQB/UjBJCg==", "mode": 436, "size": 229}}}