{"name": "get-intrinsic", "version": "1.3.0", "requiresBuild": false, "files": {".eslintrc": {"checkedAt": 1752045729221, "integrity": "sha512-1slRwqOPNqseqrqAG8+6FOpA9HyTDSXhHYvjMNAUl6CRZ1oksIsKvHsHSwe2+xtApp5NlmHY0iPt//yUjJe+Zw==", "mode": 420, "size": 647}, ".nycrc": {"checkedAt": 1752045729221, "integrity": "sha512-2vm1RFz8Ajl/OYrfoCWPJIm3Bpnf7Gyn5bha/lZx/cq+We3uMy9xj15XeP6x4wF3jf/pO7KMHAkU9mllm605xg==", "mode": 420, "size": 139}, "LICENSE": {"checkedAt": 1752045729221, "integrity": "sha512-EqKjvyq6+l4/ZpJ47jMsh+ShZM/lTvYBnKi189QfOPECJNUvRx0CVqrWYA3sn7Oh7fPITzjMnxz9Trmir4oAZg==", "mode": 420, "size": 1071}, "test/GetIntrinsic.js": {"checkedAt": 1752045729221, "integrity": "sha512-Zxs8NpB2vAYDUeek3uJbDJzo4S9pak4limsdlMy6HbFHS0sgsmGeS8yXyL8WwCc804Z6x/0nLKUXVwkBhZFm7A==", "mode": 420, "size": 8758}, "index.js": {"checkedAt": 1752045729222, "integrity": "sha512-Y+8baOyRDzkL305zXZb0l3yMqVmUCRlt29JZiftu1vBKwtK2BmM1adrL2OUuKYozOK56RAXI98Edjln5JoB2oQ==", "mode": 420, "size": 14439}, "package.json": {"checkedAt": 1752045729222, "integrity": "sha512-LYXyemN/claaKfpiydjohelWbmwUEiDUX7UjVTgr79nzKh1gSOHqPq0PxuPHipiN4C2sDBsDdWpWiEXNStaDeQ==", "mode": 420, "size": 2576}, "CHANGELOG.md": {"checkedAt": 1752045729222, "integrity": "sha512-GGmYJHeU4s2fxeNlOG1I8Hum63efDzLx9utZlVW4Fw5Lu4/n0iv0NndEMU13EY8aG2ugWYHGlrgp9+7r+FA6Xg==", "mode": 420, "size": 15537}, "README.md": {"checkedAt": 1752045729222, "integrity": "sha512-gKK4MJlg4s3igKOgb5XPHrBwny4xevN+sa9Bpxf5YZ21w+rBVIKj/sLck+EUw8PIeZrRbQFtaJ+Ad+jc8fWkgA==", "mode": 420, "size": 2791}, ".github/FUNDING.yml": {"checkedAt": 1752045729222, "integrity": "sha512-+BzZlaIXAreXEY4BcYRIQlQek7xGFFUzmxseFhgeblZ2UxajzDssgW6H0zehV1QjFAXiUQOGMp8FYXMxZTVvyA==", "mode": 420, "size": 584}}}