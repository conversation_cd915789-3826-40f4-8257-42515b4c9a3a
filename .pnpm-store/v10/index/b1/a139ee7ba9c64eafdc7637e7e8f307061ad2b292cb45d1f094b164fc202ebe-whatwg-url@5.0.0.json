{"name": "whatwg-url", "version": "5.0.0", "requiresBuild": false, "files": {"package.json": {"checkedAt": 1752045722803, "integrity": "sha512-9d1c+eoTFh8cd1XqRrspBfIP3sNm4+Omh5zhYiQNcYAvmZKo2kZkidum49vnYDqAr26hNnR7mCRSOrBVvcHxaQ==", "mode": 438, "size": 886}, "README.md": {"checkedAt": 1752045722803, "integrity": "sha512-n8GTdVLrul59s9Gr4UbNLeuAiQDKeMuFBvRIay6YEz1CDfsodgh6DJnSW8+cxTXNVygEZ71FBRz+fANF6UHAjw==", "mode": 438, "size": 5136}, "LICENSE.txt": {"checkedAt": 1752045722804, "integrity": "sha512-rkt6HfaFVoYAToPPXabowsgGiGsMm+dnexcV3eP8ibFICdkdtdHDlBWCJzAzOtTrghr9ef3pa7BVJvKhXOvH6g==", "mode": 438, "size": 1088}, "lib/URL-impl.js": {"checkedAt": 1752045722804, "integrity": "sha512-/Zp1bFVUxvdfCcRwQQC9eV7Yd7qepAEmICHSMxUHAAXwJx5AbL5PGPEWOIfKrLd650eX6qp3Cn6Jrj7CccS+4w==", "mode": 438, "size": 3804}, "lib/URL.js": {"checkedAt": 1752045722805, "integrity": "sha512-9ZR+exADE0S3UkhvyPo7ejO5sAx6UibZWB27G2N5TvOmLuEZ0oR2GLcSl0Tjta34HaL4uoVlWBAF8JcZKIORJw==", "mode": 438, "size": 4212}, "lib/public-api.js": {"checkedAt": 1752045722806, "integrity": "sha512-xc7MKBeocI4/FyC3Do38OqKey+cczczYtbwfIX/3VCCJJpLDJpPLs+coauhSYaHAfO6L8A7kVARu7NanNdgREw==", "mode": 438, "size": 625}, "lib/url-state-machine.js": {"checkedAt": 1752045722807, "integrity": "sha512-1oIM5e6vXlxTQcRXRWxmzqSwYh+TbifkjD9PJA1xRsA6vG+0SESaV6jJyGJsOrQGj/uiWV5vm3MzBanvUXQvKQ==", "mode": 438, "size": 33573}, "lib/utils.js": {"checkedAt": 1752045722807, "integrity": "sha512-L+O3+dCD2Oft9xf5T6ya+dcmUxhDKhB3klOvmCmyj1t+94FCNNNNZzI+wPzSr2776/cwGg9q0VhgF4MVTkh/DQ==", "mode": 438, "size": 562}}}