{"name": "@stdlib/utils-regexp-from-string", "version": "0.0.9", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1752045719767, "integrity": "sha512-J7jAJS6uUMo84Cq3xWcGZMDIJOA+s9oQifPwoA0j5kipVry59TZFxteWdKh8TMhtEIXcM1kRvgIQ1pEzaxIYVw==", "mode": 420, "size": 10174}, "NOTICE": {"checkedAt": 1752045719767, "integrity": "sha512-4cuPRRYzgfVMbZIC790aV9oIoAEVC+4xyLwcB73N6IHP34DxsSH2gT61hzrQQsrrPKyZ3lwkqLKxnRVCIg2vCw==", "mode": 420, "size": 44}, "lib/from_string.js": {"checkedAt": 1752045719797, "integrity": "sha512-I+7Z3Qev6pRN15A6hdDa6a8WlotJTiUbx6k02ivi0q6sGjgk2OuaYioFGI2url8Sd0r+dHXmDR85ykkA1l+NGw==", "mode": 420, "size": 1558}, "lib/index.js": {"checkedAt": 1752045719797, "integrity": "sha512-nw6ZHFYneVf1fvYjCKy+kaA+Ve7wYWB1mnqeGtvUJh6ihGUP1u+QDe1u8l1Gu+XzQh3ml6Mt1kjPI2LzeJ0Agw==", "mode": 420, "size": 1003}, "package.json": {"checkedAt": 1752045719798, "integrity": "sha512-ticic9Hg+5/lI81iSo7g0a+/sMhDuw67gNImHBWRFw3BxpYTvt/wH0gP13qe2qc5q15SPDoP/Up9R+XaZNNm/A==", "mode": 420, "size": 1858}, "README.md": {"checkedAt": 1752045719798, "integrity": "sha512-AfiyqtlR3OSboVB6vd6Ebx0D6vKcY8l3Dh9+wG8Z7dK059PrlSMOh5TCKYRKIS3vURrKM5XrY5S5kPfDQurj0w==", "mode": 420, "size": 5218}, "docs/types/index.d.ts": {"checkedAt": 1752045719798, "integrity": "sha512-ahpuzHEd61+aKBnriOvNjThBqoN1lU91xRmIoGeGsgI24c5sT2kB8WgNCsq5ST+uzLhvBZxRZYRfJUDX6NeW2A==", "mode": 420, "size": 1184}, "docs/types/test.ts": {"checkedAt": 1752045719798, "integrity": "sha512-kvZYXmTw0g/EXVwleiTXTVV5UkS3lPlud4qpVu4S4SIndILjupBFhZzbvTT5Qhf8LRCPfZ/ORZ/m09/919nEDg==", "mode": 420, "size": 1406}, "docs/repl.txt": {"checkedAt": 1752045719862, "integrity": "sha512-ZCE3xHT7nu6uB9tUIA9Z/Sn2oeArGiugEy+DPUfdSKtv4CXU0LyGjUESoXCz6dOG5Q/2Lkhw6nXGjgT4SUFOIA==", "mode": 420, "size": 544}}}