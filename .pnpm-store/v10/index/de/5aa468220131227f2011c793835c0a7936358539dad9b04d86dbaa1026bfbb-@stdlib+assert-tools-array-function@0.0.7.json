{"name": "@stdlib/assert-tools-array-function", "version": "0.0.7", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1752045744178, "integrity": "sha512-giZYqp2QbwsOHNySg9FuTpE8Yyro5HyjpXkP9Ck5aqbhCvG1sKLbnlYjh5mR2+1rIt/vv9FN8ffyrszTfwbkMg==", "mode": 420, "size": 23963}, "NOTICE": {"checkedAt": 1752045744178, "integrity": "sha512-4cuPRRYzgfVMbZIC790aV9oIoAEVC+4xyLwcB73N6IHP34DxsSH2gT61hzrQQsrrPKyZ3lwkqLKxnRVCIg2vCw==", "mode": 420, "size": 44}, "lib/arrayfcn.js": {"checkedAt": 1752045744183, "integrity": "sha512-RRNGSHshPgVEx5TRn4Ti6USEh/eK1l6lm15OE+SxM0Cgs1GoXwWNfiiJFLmZdjLztJnT7yDTvLpBMUIrh2Votw==", "mode": 420, "size": 1963}, "lib/index.js": {"checkedAt": 1752045744184, "integrity": "sha512-QDMrCD33Ts36YiSrF+aejoQtNGR+6wHD/zX/KktpBZ/zVOq65XLFoGlLrhJ1IdVwHFX2TP+JxfKoi9P6kUszzg==", "mode": 420, "size": 1201}, "package.json": {"checkedAt": 1752045744184, "integrity": "sha512-eXSdONawrPHUOlBfunZ47iWjYEfg+rwtCbpgw8PLj4YKfZLt4YbmuvNQALSt+8v5AjraCMPGxJ6/VfCskJAlgw==", "mode": 420, "size": 1968}, "README.md": {"checkedAt": 1752045744184, "integrity": "sha512-Wgr5VcPgEfuwLHwBK/5JDNCKXd6SpCE5h3hWDZddws+exxR2Tj/F37QRBJDqd46OdKPGHRXZDYPrxo4i6byCiQ==", "mode": 420, "size": 5479}, "docs/types/index.d.ts": {"checkedAt": 1752045744184, "integrity": "sha512-ftVCQe+LpYqtoyLdJzQxdCMfcZvehTIQh+GbT4qzUa/2N6e3JkCBhvLIYXdcFq81bC0iOxOENii9Z9KUu4EA3g==", "mode": 420, "size": 1793}, "docs/types/test.ts": {"checkedAt": 1752045744184, "integrity": "sha512-GYCcE2z7NCG096lxFRKteXWOvgqfl9zRDH/eBIWbMWtin5mJ6SCPpggq3G6UQtUkY7UsmgADKOz+luSC8UzTew==", "mode": 420, "size": 1391}, "docs/repl.txt": {"checkedAt": 1752045744184, "integrity": "sha512-rQ/dOHeVsXEa/iPur/Ilth9EYr9d1R05Y1XFLdb4YdzSkQzyn98T6Dt2phw+1pk83SSQCcH04JY6RYv4E1OzTQ==", "mode": 420, "size": 1057}}}